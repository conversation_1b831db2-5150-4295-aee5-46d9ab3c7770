import logging
import uuid
import asyncio
import os
import json
from datetime import datetime
from typing import Dict, List, Optional, AsyncGenerator
from enum import Enum

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, BackgroundTasks
from fastapi.responses import J<PERSON>NResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

from LoggerManager import LoggerManager
from CKGRetriever import CKGRetriever
from EnvironmentService import EnvironmentService
from Planner import PlannerState
from Planner import graph
from Config import *


# 枚举类定义
class TaskStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class StepStatus(str, Enum):
    WAITING = "waiting"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


# 数据模型定义
class TestGenerationRequest(BaseModel):
    method_id: int
    url: str
    username: str
    password: str
    limit: int = 50
    base_path: Optional[str] = None


class TaskInfo(BaseModel):
    task_id: str
    status: TaskStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    progress: float = 0.0
    current_step: Optional[str] = None
    total_steps: int = 0
    completed_steps: int = 0
    error_message: Optional[str] = None
    request_data: TestGenerationRequest


class StepInfo(BaseModel):
    step_name: str
    status: StepStatus
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    duration: Optional[float] = None
    output: Optional[Dict] = None
    error_message: Optional[str] = None


class TaskProgress(BaseModel):
    task_id: str
    status: TaskStatus
    progress: float
    current_step: Optional[str] = None
    steps: List[StepInfo] = []
    completed_steps: int = 0
    estimated_remaining_time: Optional[float] = None


class WebSocketMessage(BaseModel):
    type: str  # "progress", "step_update", "log", "result", "error"
    task_id: str
    data: Dict


class TaskResult(BaseModel):
    task_id: str
    status: TaskStatus
    test_cases: List[Dict] = []
    summary: Optional[Dict] = None
    error_message: Optional[str] = None


# 全局任务管理器
class TaskManager:
    def __init__(self):
        self.tasks: Dict[str, TaskInfo] = {}
        self.task_progress: Dict[str, TaskProgress] = {}
        self.task_results: Dict[str, TaskResult] = {}
        self.websocket_connections: Dict[str, List[WebSocket]] = {}

    def create_task(self, request: TestGenerationRequest) -> str:
        task_id = str(uuid.uuid4())
        task_info = TaskInfo(
            task_id=task_id,
            status=TaskStatus.PENDING,
            created_at=datetime.now(),
            request_data=request,
            total_steps=5  # 根据工作流定义的步骤数
        )
        self.tasks[task_id] = task_info

        # 初始化进度信息
        steps = [
            StepInfo(step_name="initState", status=StepStatus.WAITING),
            StepInfo(step_name="methodAnalyzer", status=StepStatus.WAITING),
            StepInfo(step_name="testPointsGenerator", status=StepStatus.WAITING),
            StepInfo(step_name="testCaseGenerator", status=StepStatus.WAITING),
            StepInfo(step_name="testCaseAcceptor", status=StepStatus.WAITING)
        ]

        self.task_progress[task_id] = TaskProgress(
            task_id=task_id,
            status=TaskStatus.PENDING,
            progress=0.0,
            steps=steps
        )

        return task_id

    def get_task(self, task_id: str) -> Optional[TaskInfo]:
        return self.tasks.get(task_id)

    def get_task_progress(self, task_id: str) -> Optional[TaskProgress]:
        return self.task_progress.get(task_id)

    def update_task_status(self, task_id: str, status: TaskStatus, error_message: Optional[str] = None):
        if task_id in self.tasks:
            self.tasks[task_id].status = status
            self.task_progress[task_id].status = status

            if status == TaskStatus.RUNNING and not self.tasks[task_id].started_at:
                self.tasks[task_id].started_at = datetime.now()
            elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                self.tasks[task_id].completed_at = datetime.now()

            if error_message:
                self.tasks[task_id].error_message = error_message

    def update_step_status(self, task_id: str, step_name: str, status: StepStatus,
                          output: Optional[Dict] = None, error_message: Optional[str] = None):
        if task_id in self.task_progress:
            progress = self.task_progress[task_id]

            # 更新步骤状态
            for step in progress.steps:
                if step.step_name == step_name:
                    step.status = status
                    if status == StepStatus.RUNNING and not step.started_at:
                        step.started_at = datetime.now()
                    elif status in [StepStatus.COMPLETED, StepStatus.FAILED, StepStatus.SKIPPED]:
                        step.completed_at = datetime.now()
                        if step.started_at:
                            step.duration = (step.completed_at - step.started_at).total_seconds()

                    if output:
                        step.output = output
                    if error_message:
                        step.error_message = error_message
                    break

            # 更新总体进度
            completed_steps = sum(1 for step in progress.steps if step.status == StepStatus.COMPLETED)
            progress.completed_steps = completed_steps
            progress.progress = (completed_steps / len(progress.steps)) * 100
            progress.current_step = step_name

            # 更新任务信息
            if task_id in self.tasks:
                self.tasks[task_id].progress = progress.progress
                self.tasks[task_id].current_step = step_name
                self.tasks[task_id].completed_steps = completed_steps

    async def broadcast_to_task_subscribers(self, task_id: str, message: WebSocketMessage):
        """向订阅特定任务的WebSocket连接广播消息"""
        if task_id in self.websocket_connections:
            disconnected = []
            for websocket in self.websocket_connections[task_id]:
                try:
                    await websocket.send_text(message.model_dump_json())
                except:
                    disconnected.append(websocket)

            # 清理断开的连接
            for ws in disconnected:
                self.websocket_connections[task_id].remove(ws)

    def add_websocket_connection(self, task_id: str, websocket: WebSocket):
        """添加WebSocket连接到任务订阅列表"""
        if task_id not in self.websocket_connections:
            self.websocket_connections[task_id] = []
        self.websocket_connections[task_id].append(websocket)

    def remove_websocket_connection(self, task_id: str, websocket: WebSocket):
        """从任务订阅列表中移除WebSocket连接"""
        if task_id in self.websocket_connections:
            try:
                self.websocket_connections[task_id].remove(websocket)
            except ValueError:
                pass


# 全局任务管理器实例
task_manager = TaskManager()


def get_graph_retriever(url, username, password):
    return CKGRetriever(url, username, password)


def write_json(data, path):
    """保存JSON数据到文件"""
    os.makedirs(os.path.dirname(path), exist_ok=True)
    with open(path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=4)


async def generate_test_case_with_progress(
    task_id: str,
    request: TestGenerationRequest
) -> TaskResult:
    """
    执行测试用例生成任务，支持进度追踪和WebSocket通信
    """
    try:
        # 更新任务状态为运行中
        task_manager.update_task_status(task_id, TaskStatus.RUNNING)
        await task_manager.broadcast_to_task_subscribers(
            task_id,
            WebSocketMessage(type="progress", task_id=task_id, data={"status": "running", "message": "任务开始执行"})
        )

        # 初始化日志器
        logger_manager = LoggerManager()

        # 设置测试方法、环境与图结构
        config = {"recursion_limit": request.limit}
        graph_retriever = CKGRetriever(request.url, request.username, request.password)
        method = graph_retriever.search_method_by_id(request.method_id)
        envServer = EnvironmentService()

        # 安全地访问method属性
        method_absolute_path = getattr(method, 'absolute_path', '')
        method_id = getattr(method, 'id', request.method_id)
        method_name = getattr(method, 'name', f'method_{request.method_id}')
        method_package_name = getattr(method, 'package_name', '')
        method_signature = getattr(method, 'signature', '')
        method_full_qualified_name = getattr(method, 'full_qualified_name', '')
        method_content = getattr(method, 'content', '')
        method_class_name = getattr(method, 'class_name', '')
        method_start_line = getattr(method, 'start_line', 0)
        method_end_line = getattr(method, 'end_line', 0)

        if method_absolute_path:
            envServer.set_base_path(method_absolute_path.split("src")[0])
            inject_path = method_absolute_path.replace("src/main", "src/test")
            inject_dir = inject_path[:inject_path.rfind("/")]
            envServer.set_inject_dir(inject_dir)

        graph_retriever.change_focal_method_id(int(method_id))

        # 设置日志路径
        base_path = request.base_path or (method_absolute_path.split("src")[0] if method_absolute_path else ".")
        log_dir = os.path.join(base_path, f"result/stream/{request.method_id}_{method_name}")
        result_dir = os.path.join(base_path, f"result/report/")
        os.makedirs(log_dir, exist_ok=True)
        log_path = os.path.join(log_dir, f"{request.method_id}_{method_name}_total.log")
        logger_manager.change_log_path(log_path)
        logger = logger_manager.logger

        # 广播基本方法信息
        method_info = {
            "package_name": method_package_name,
            "method_name": method_name,
            "method_signature": method_signature,
            "full_qualified_name": method_full_qualified_name,
            "absolute_path": method_absolute_path
        }

        await task_manager.broadcast_to_task_subscribers(
            task_id,
            WebSocketMessage(type="log", task_id=task_id, data={"message": "================= TEST GENERATE START ================="})
        )

        await task_manager.broadcast_to_task_subscribers(
            task_id,
            WebSocketMessage(type="log", task_id=task_id, data={"method_info": method_info})
        )

        # 构造初始状态
        state = PlannerState(
            envServer=envServer,
            package_name=method_package_name,
            method_id=int(method_id),
            method_code=method_content,
            method_signature=method_signature,
            class_name=method_class_name,
            full_method_name=method_full_qualified_name,
            start_line=method_start_line,
            end_line=method_end_line
        )

        # 执行 LangGraph 工作流
        test_cases = []
        for event in graph.stream(state, config, subgraphs=True):
            current_node = list(event[1].keys())[0]

            # 更新步骤状态
            task_manager.update_step_status(task_id, current_node, StepStatus.RUNNING)

            # 广播步骤更新
            await task_manager.broadcast_to_task_subscribers(
                task_id,
                WebSocketMessage(
                    type="step_update",
                    task_id=task_id,
                    data={
                        "step_name": current_node,
                        "status": "running",
                        "graph": str(event[0])
                    }
                )
            )

            # 处理步骤输出
            step_output = {}
            for value in event[1].values():
                try:
                    if isinstance(value, dict) and "messages" in value:
                        step_output["messages"] = [m.pretty_repr() for m in value["messages"]]
                    else:
                        step_output["data"] = value
                except Exception as e:
                    step_output["error"] = str(e)

            # 广播步骤日志
            await task_manager.broadcast_to_task_subscribers(
                task_id,
                WebSocketMessage(
                    type="log",
                    task_id=task_id,
                    data={
                        "step": current_node,
                        "output": step_output
                    }
                )
            )

            # 完成步骤
            task_manager.update_step_status(task_id, current_node, StepStatus.COMPLETED, step_output)

            # 流程结束节点：testCaseAcceptor
            if "testCaseAcceptor" in event[1]:
                # 保存结果
                result_data = event[1]['testCaseAcceptor']
                write_json(result_data, os.path.join(result_dir, f"{request.method_id}_{method_name}_test_case.json"))

                # 处理测试用例
                for i, test_case in enumerate(result_data['test_cases'], 1):
                    report_obj = {"index": i}
                    for field in ['test_result', 'find_bug', 'test_case', 'test_point',
                                  'test_report', 'coverage_report', 'mutation_report']:
                        try:
                            report_obj[field] = test_case.get(field)
                        except Exception as e:
                            report_obj[field] = f"Error extracting field: {e}"

                    test_cases.append(report_obj)

                # 广播最终结果
                await task_manager.broadcast_to_task_subscribers(
                    task_id,
                    WebSocketMessage(
                        type="result",
                        task_id=task_id,
                        data={
                            "test_cases": test_cases,
                            "summary": {
                                "total_test_cases": len(test_cases),
                                "method_info": method_info
                            }
                        }
                    )
                )
                break

        # 任务完成
        task_manager.update_task_status(task_id, TaskStatus.COMPLETED)

        # 创建任务结果
        result = TaskResult(
            task_id=task_id,
            status=TaskStatus.COMPLETED,
            test_cases=test_cases,
            summary={
                "total_test_cases": len(test_cases),
                "method_info": method_info
            }
        )

        task_manager.task_results[task_id] = result

        await task_manager.broadcast_to_task_subscribers(
            task_id,
            WebSocketMessage(type="progress", task_id=task_id, data={"status": "completed", "message": "任务完成"})
        )

        return result

    except Exception as e:
        # 任务失败
        error_message = str(e)
        task_manager.update_task_status(task_id, TaskStatus.FAILED, error_message)

        result = TaskResult(
            task_id=task_id,
            status=TaskStatus.FAILED,
            error_message=error_message
        )

        task_manager.task_results[task_id] = result

        await task_manager.broadcast_to_task_subscribers(
            task_id,
            WebSocketMessage(
                type="error",
                task_id=task_id,
                data={"error": error_message}
            )
        )

        return result

# FastAPI 应用初始化
app = FastAPI(
    title="TestAgent API",
    description="智能测试用例生成系统 API",
    version="2.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# API 端点定义

@app.post("/tasks", response_model=Dict[str, str])
async def create_task(request: TestGenerationRequest, background_tasks: BackgroundTasks):
    """
    创建新的测试生成任务
    """
    try:
        task_id = task_manager.create_task(request)

        # 在后台执行任务
        background_tasks.add_task(generate_test_case_with_progress, task_id, request)

        return {"task_id": task_id, "status": "created"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/tasks/{task_id}", response_model=TaskInfo)
async def get_task_info(task_id: str):
    """
    获取任务信息
    """
    task = task_manager.get_task(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    return task


@app.get("/tasks/{task_id}/progress", response_model=TaskProgress)
async def get_task_progress(task_id: str):
    """
    获取任务进度
    """
    progress = task_manager.get_task_progress(task_id)
    if not progress:
        raise HTTPException(status_code=404, detail="Task not found")
    return progress


@app.get("/tasks/{task_id}/result", response_model=TaskResult)
async def get_task_result(task_id: str):
    """
    获取任务结果
    """
    result = task_manager.task_results.get(task_id)
    if not result:
        task = task_manager.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        elif task.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
            raise HTTPException(status_code=202, detail="Task is still running")
        else:
            raise HTTPException(status_code=404, detail="Task result not found")
    return result


@app.get("/tasks", response_model=List[TaskInfo])
async def list_tasks(limit: int = 50, offset: int = 0):
    """
    获取任务列表
    """
    tasks = list(task_manager.tasks.values())
    # 按创建时间倒序排列
    tasks.sort(key=lambda x: x.created_at, reverse=True)
    return tasks[offset:offset + limit]


@app.delete("/tasks/{task_id}")
async def cancel_task(task_id: str):
    """
    取消任务
    """
    task = task_manager.get_task(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
        raise HTTPException(status_code=400, detail="Task cannot be cancelled")

    task_manager.update_task_status(task_id, TaskStatus.CANCELLED)
    return {"message": "Task cancelled successfully"}


@app.websocket("/ws/{task_id}")
async def websocket_endpoint(websocket: WebSocket, task_id: str):
    """
    WebSocket端点，用于实时接收任务进度和结果
    """
    await websocket.accept()

    # 检查任务是否存在
    task = task_manager.get_task(task_id)
    if not task:
        await websocket.send_text(json.dumps({
            "type": "error",
            "data": {"error": "Task not found"}
        }))
        await websocket.close()
        return

    # 添加WebSocket连接到任务订阅列表
    task_manager.add_websocket_connection(task_id, websocket)

    try:
        # 发送当前任务状态
        progress = task_manager.get_task_progress(task_id)
        if progress:
            await websocket.send_text(
                WebSocketMessage(
                    type="progress",
                    task_id=task_id,
                    data=progress.model_dump()
                ).model_dump_json()
            )

        # 如果任务已完成，发送结果
        if task.status == TaskStatus.COMPLETED and task_id in task_manager.task_results:
            result = task_manager.task_results[task_id]
            await websocket.send_text(
                WebSocketMessage(
                    type="result",
                    task_id=task_id,
                    data=result.model_dump()
                ).model_dump_json()
            )

        # 保持连接活跃
        while True:
            try:
                # 等待客户端消息（心跳检测）
                await asyncio.wait_for(websocket.receive_text(), timeout=30.0)
            except asyncio.TimeoutError:
                # 发送心跳
                await websocket.send_text(json.dumps({"type": "ping"}))
            except WebSocketDisconnect:
                break

    except WebSocketDisconnect:
        pass
    finally:
        # 清理WebSocket连接
        task_manager.remove_websocket_connection(task_id, websocket)


@app.post("/generate_test_case_stream")
async def generate_test_case_stream(request: TestGenerationRequest):
    """
    流式生成测试用例端点 (向后兼容)
    """
    from fastapi.responses import StreamingResponse

    async def generate_stream():
        try:
            # 创建任务
            task_id = task_manager.create_task(request)

            # 开始生成测试用例
            yield f"data: 任务已创建，ID: {task_id}\n\n"
            yield f"data: ================= TEST GENERATE START =================\n\n"

            # 初始化日志器
            logger_manager = LoggerManager()

            # 设置测试方法、环境与图结构
            config = {"recursion_limit": request.limit}
            graph_retriever = CKGRetriever(request.url, request.username, request.password)
            method = graph_retriever.search_method_by_id(request.method_id)
            envServer = EnvironmentService()

            # 安全地访问method属性
            method_absolute_path = getattr(method, 'absolute_path', '')
            method_id = getattr(method, 'id', request.method_id)
            method_name = getattr(method, 'name', f'method_{request.method_id}')
            method_package_name = getattr(method, 'package_name', '')
            method_signature = getattr(method, 'signature', '')
            method_full_qualified_name = getattr(method, 'full_qualified_name', '')
            method_content = getattr(method, 'content', '')
            method_class_name = getattr(method, 'class_name', '')
            method_start_line = getattr(method, 'start_line', 0)
            method_end_line = getattr(method, 'end_line', 0)

            if method_absolute_path:
                envServer.set_base_path(method_absolute_path.split("src")[0])
                inject_path = method_absolute_path.replace("src/main", "src/test")
                inject_dir = inject_path[:inject_path.rfind("/")]
                envServer.set_inject_dir(inject_dir)

            graph_retriever.change_focal_method_id(int(method_id))

            # 设置日志路径
            base_path = request.base_path or (method_absolute_path.split("src")[0] if method_absolute_path else ".")
            log_dir = os.path.join(base_path, f"result/stream/{request.method_id}_{method_name}")
            result_dir = os.path.join(base_path, f"result/report/")
            os.makedirs(log_dir, exist_ok=True)
            log_path = os.path.join(log_dir, f"{request.method_id}_{method_name}_total.log")
            logger_manager.change_log_path(log_path)

            # 输出方法信息
            yield f"data: package_name: {method_package_name}\n\n"
            yield f"data: method_name: {method_name}\n\n"
            yield f"data: method_signature: {method_signature}\n\n"
            yield f"data: full_qualified_name: {method_full_qualified_name}\n\n"
            yield f"data: absolute_path: {method_absolute_path}\n\n"

            # 更新任务状态
            task_manager.update_task_status(task_id, TaskStatus.RUNNING)

            # 构造初始状态
            state = PlannerState(
                envServer=envServer,
                package_name=method_package_name,
                method_id=int(method_id),
                method_code=method_content,
                method_signature=method_signature,
                class_name=method_class_name,
                full_method_name=method_full_qualified_name,
                start_line=method_start_line,
                end_line=method_end_line
            )

            # 执行 LangGraph 工作流
            test_cases = []
            for event in graph.stream(state, config, subgraphs=True):
                current_node = list(event[1].keys())[0]

                # 更新步骤状态
                task_manager.update_step_status(task_id, current_node, StepStatus.RUNNING)

                yield f"data: ========================== Graph: {str(event[0])} =====================\n\n"
                yield f"data: Current Node: {current_node}\n\n"

                # 处理步骤输出
                for value in event[1].values():
                    try:
                        if isinstance(value, dict) and "messages" in value:
                            for m in value["messages"]:
                                yield f"data: {m.pretty_repr()}\n\n"
                        else:
                            yield f"data: {str(value)}\n\n"
                    except Exception as e:
                        yield f"data: Error processing output: {str(e)}\n\n"

                # 完成步骤
                task_manager.update_step_status(task_id, current_node, StepStatus.COMPLETED)

                # 流程结束节点：testCaseAcceptor
                if "testCaseAcceptor" in event[1]:
                    # 保存结果
                    result_data = event[1]['testCaseAcceptor']
                    write_json(result_data, os.path.join(result_dir, f"{request.method_id}_{method_name}_test_case.json"))

                    # 处理测试用例
                    for i, test_case in enumerate(result_data['test_cases'], 1):
                        report_obj = {"index": i}
                        for field in ['test_result', 'find_bug', 'test_case', 'test_point',
                                      'test_report', 'coverage_report', 'mutation_report']:
                            try:
                                report_obj[field] = test_case.get(field)
                            except Exception as e:
                                report_obj[field] = f"Error extracting field: {e}"

                        test_cases.append(report_obj)

                        # 输出结构化报告
                        yield f"data: ##REPORT##:{json.dumps(report_obj, ensure_ascii=False)}\n\n"

                        # 输出测试用例详情
                        yield f"data: ===================== Test Report {i} =====================\n\n"
                        yield f"data: test result: {test_case.get('test_result', 'N/A')}\n\n"
                        yield f"data: find bugs: {test_case.get('find_bug', 'N/A')}\n\n"
                        yield f"data: test case: {test_case.get('test_case', 'N/A')}\n\n"
                        yield f"data: test point: {test_case.get('test_point', 'N/A')}\n\n"
                        yield f"data: test report: {test_case.get('test_report', 'N/A')}\n\n"
                        yield f"data: coverage report: {test_case.get('coverage_report', 'N/A')}\n\n"
                        yield f"data: mutation report: {test_case.get('mutation_report', 'N/A')}\n\n"

                    break

            # 任务完成
            task_manager.update_task_status(task_id, TaskStatus.COMPLETED)
            yield f"data: ===================== TEST GENERATE END =====================\n\n"
            yield f"data: [Done] 生成了 {len(test_cases)} 个测试用例\n\n"

        except Exception as e:
            # 任务失败
            if 'task_id' in locals():
                task_manager.update_task_status(task_id, TaskStatus.FAILED, str(e))
            yield f"data: Error: {str(e)}\n\n"
            yield f"data: [Done] 任务执行失败\n\n"

    return StreamingResponse(generate_stream(), media_type="text/plain")


@app.post("/generate_test_case_stream_java")
async def generate_test_case_stream_java(request: TestGenerationRequest):
    """
    Java测试用例流式生成端点 (向后兼容)
    """
    # 重定向到通用的流式端点
    return await generate_test_case_stream(request)


@app.get("/health")
async def health_check():
    """
    健康检查端点
    """
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "active_tasks": len([t for t in task_manager.tasks.values() if t.status == TaskStatus.RUNNING]),
        "total_tasks": len(task_manager.tasks)
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)