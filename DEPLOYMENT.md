# TestAgent v2.0 部署指南

## 🚀 快速开始

### 方法1: 本地开发环境

1. **安装依赖**
```bash
pip install -r requirements_v2.txt
```

2. **启动Neo4j数据库**
```bash
# 使用Docker启动Neo4j
docker run -d \
  --name neo4j \
  -p 7474:7474 -p 7687:7687 \
  -e NEO4J_AUTH=neo4j/123456 \
  neo4j:5.15-community
```

3. **启动服务**
```bash
python start_server.py
```

4. **访问服务**
- API文档: http://localhost:8000/docs
- 演示页面: 打开 demo.html
- 健康检查: http://localhost:8000/health

### 方法2: Docker部署

1. **构建镜像**
```bash
docker build -t testagent:v2.0 .
```

2. **使用Docker Compose启动完整服务栈**
```bash
docker-compose up -d
```

3. **查看服务状态**
```bash
docker-compose ps
docker-compose logs testagent
```

## 🔧 配置说明

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `TESTAGENT_ENV` | development | 运行环境 (development/production/test) |
| `TESTAGENT_HOST` | 0.0.0.0 | 服务器地址 |
| `TESTAGENT_PORT` | 8000 | 服务器端口 |
| `TESTAGENT_NEO4J_URL` | bolt://localhost:7687 | Neo4j连接URL |
| `TESTAGENT_NEO4J_USERNAME` | neo4j | Neo4j用户名 |
| `TESTAGENT_NEO4J_PASSWORD` | 123456 | Neo4j密码 |
| `TESTAGENT_MAX_CONCURRENT_TASKS` | 10 | 最大并发任务数 |
| `TESTAGENT_LOG_LEVEL` | INFO | 日志级别 |

### 配置文件

创建 `.env` 文件来覆盖默认配置：

```env
TESTAGENT_ENV=production
TESTAGENT_HOST=0.0.0.0
TESTAGENT_PORT=8000
TESTAGENT_NEO4J_URL=bolt://localhost:7687
TESTAGENT_NEO4J_USERNAME=neo4j
TESTAGENT_NEO4J_PASSWORD=your_password
TESTAGENT_MAX_CONCURRENT_TASKS=20
TESTAGENT_LOG_LEVEL=INFO
TESTAGENT_OPENAI_API_KEY=your_openai_key
```

## 🏗️ 生产环境部署

### 1. 系统要求

- **CPU**: 4核心以上
- **内存**: 8GB以上
- **存储**: 50GB以上可用空间
- **网络**: 稳定的互联网连接
- **操作系统**: Linux (推荐 Ubuntu 20.04+)

### 2. 安全配置

1. **更改默认密码**
```bash
# Neo4j密码
export TESTAGENT_NEO4J_PASSWORD=your_secure_password

# 如果使用Redis
export REDIS_PASSWORD=your_redis_password
```

2. **配置防火墙**
```bash
# 只开放必要端口
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 8000/tcp  # 如果直接暴露API
ufw enable
```

3. **使用HTTPS**
- 配置SSL证书
- 使用Nginx反向代理
- 启用CORS安全策略

### 3. 性能优化

1. **调整并发设置**
```env
TESTAGENT_MAX_CONCURRENT_TASKS=20
TESTAGENT_WORKER_PROCESSES=4
TESTAGENT_WORKER_CONNECTIONS=1000
```

2. **数据库优化**
```env
# Neo4j内存配置
NEO4J_dbms_memory_heap_initial__size=2g
NEO4J_dbms_memory_heap_max__size=4g
NEO4J_dbms_memory_pagecache_size=2g
```

3. **日志管理**
```env
TESTAGENT_LOG_LEVEL=WARNING
TESTAGENT_LOG_MAX_SIZE=100MB
TESTAGENT_LOG_BACKUP_COUNT=10
```

### 4. 监控和维护

1. **健康检查**
```bash
# 检查服务状态
curl http://localhost:8000/health

# 检查任务统计
curl http://localhost:8000/tasks
```

2. **日志监控**
```bash
# 查看实时日志
docker-compose logs -f testagent

# 查看错误日志
grep ERROR log/*.log
```

3. **数据备份**
```bash
# 备份Neo4j数据
docker exec testagent-neo4j neo4j-admin dump --database=neo4j --to=/backups/neo4j-backup.dump

# 备份结果文件
tar -czf results-backup-$(date +%Y%m%d).tar.gz result/
```

## 🔍 故障排除

### 常见问题

1. **服务无法启动**
```bash
# 检查端口占用
netstat -tlnp | grep 8000

# 检查依赖
python -c "import fastapi, uvicorn, websockets"
```

2. **Neo4j连接失败**
```bash
# 检查Neo4j状态
docker logs testagent-neo4j

# 测试连接
python -c "from neo4j import GraphDatabase; print('连接成功' if GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', '123456')).verify_connectivity() else '连接失败')"
```

3. **WebSocket连接问题**
```bash
# 检查防火墙设置
ufw status

# 测试WebSocket
wscat -c ws://localhost:8000/ws/test-task-id
```

4. **任务执行失败**
```bash
# 查看任务日志
ls -la result/stream/

# 检查系统资源
htop
df -h
```

### 性能调优

1. **内存不足**
- 减少并发任务数
- 增加系统内存
- 优化Neo4j内存配置

2. **响应缓慢**
- 检查网络延迟
- 优化数据库查询
- 增加工作进程数

3. **磁盘空间不足**
- 清理旧日志文件
- 压缩结果文件
- 配置日志轮转

## 📊 监控指标

### 关键指标

- **任务成功率**: 成功完成的任务比例
- **平均响应时间**: API请求的平均响应时间
- **并发任务数**: 同时运行的任务数量
- **系统资源使用率**: CPU、内存、磁盘使用情况
- **错误率**: 系统错误的发生频率

### 监控工具

- **Prometheus**: 指标收集
- **Grafana**: 可视化仪表板
- **日志分析**: ELK Stack或类似工具
- **告警系统**: 基于阈值的自动告警

## 🔄 升级和维护

### 版本升级

1. **备份数据**
2. **停止服务**
3. **更新代码**
4. **迁移数据库**（如需要）
5. **重启服务**
6. **验证功能**

### 定期维护

- **每日**: 检查服务状态和错误日志
- **每周**: 清理临时文件和旧日志
- **每月**: 备份重要数据和配置
- **每季度**: 更新依赖包和安全补丁

## 📞 技术支持

如果遇到问题，请：

1. 查看日志文件
2. 检查配置设置
3. 参考故障排除指南
4. 提交Issue到项目仓库
