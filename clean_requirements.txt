ace_tools==0.0
aiohappyeyeballs==2.4.6
aiohttp==3.11.12
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.9.0
appdirs==1.4.4
async-timeout==4.0.3
attrs==25.1.0
baml-py==0.75.0
cached-property==2.0.1
certifi==2025.1.31
charset-normalizer==3.4.1
click==8.1.8
colorama==0.4.6
configparser==7.2.0
cramjam==2.10.0
dataclasses-json==0.6.7
datasets==3.5.0
diff_cover==9.2.4
dill==0.3.8
distro==1.9.0
exceptiongroup==1.2.2
fastapi==0.115.12
fastparquet==2024.11.0
filelock==3.18.0
frozenlist==1.5.0
fsspec==2024.12.0
h11==0.14.0
h5py==3.13.0
httpcore==1.0.7
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.30.2
hypothesis==6.130.12
idna==3.10
iniconfig==2.1.0
javalang==0.13.0
Jinja2==3.1.6
jiter==0.8.2
joblib==1.4.2
jsonpatch==1.33
jsonpointer==3.0.0
langchain==0.3.18
langchain-community==0.3.17
langchain-core==0.3.45
langchain-ollama==0.2.3
langchain-openai==0.3.4
langchain-text-splitters==0.3.6
langgraph==0.3.11
langgraph-checkpoint==2.0.20
langgraph-prebuilt==0.1.3
langgraph-sdk==0.1.57
langsmith==0.3.15
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib==3.10.0
msgpack==1.1.0
multidict==6.1.0
multiprocess==0.70.16
mypy-extensions==1.0.0
neo4j==5.28.1
networkx==3.4.2
numpy==1.26.4
ollama==0.4.7
openai==1.61.1
orjson==3.10.15
oyaml==1.0
pathspec==0.12.1
pluggy==1.5.0
propcache==0.2.1
protobuf==4.25.3
-e git+https://github.com/pvlib/pvlib-python.git@04a523fafbd61bc2e49420963b84ed8e2bd1b3cf#egg=pvlib
pyarrow==19.0.1
pydantic==2.10.6
pydantic-settings==2.7.1
pydantic_core==2.27.2
Pygments==2.19.1
python-dotenv==1.0.1
python-socks==2.7.1
PyYAML==6.0.2
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
scikit-learn==1.6.1
scipy==1.15.2
sniffio==1.3.1
socksio==1.0.0
sortedcontainers==2.4.0
SQLAlchemy==2.0.38
-e git+https://github.com/sqlfluff/sqlfluff.git@14e1a23a3166b9a645a16de96f694c77a5d4abb7#egg=sqlfluff
sseclient-py==1.8.0
starlette==0.46.2
tblib==3.1.0
tenacity==9.0.0
threadpoolctl==3.6.0
tiktoken==0.8.0
toml==0.10.2
tomli==2.2.1
tqdm==4.67.1
typing-inspect==0.9.0
typing_extensions==4.12.2
urllib3==2.3.0
uvicorn==0.34.1
xxhash==3.5.0
yarl==1.18.3
zstandard==0.23.0
