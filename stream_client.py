import requests
import sseclient
import json

def test_streaming_api():
    # 替换为你的服务地址
    url = 'http://localhost:8000/generate_test_case_stream'

    # 请求体参数
    payload = {
        "method_id": 44777,
        "url": "bolt://localhost:7687",
        "username": "neo4j",
        "password": "123456",
        "limit": 50
    }

    # 保存结构化报告的列表
    all_reports = []

    # 发起 POST 请求，并读取 SSE 流
    with requests.post(url, json=payload, stream=True) as response:
        client = sseclient.SSEClient(response)
        print("✅ Connected to stream. Output:\n")

        for event in client.events():
            line = event.data.strip()

            # 打印所有内容（用于调试可视化）
            print(line)

            # 捕获结构化 JSON 报告
            if line.startswith("##REPORT##:"):
                try:
                    json_obj = json.loads(line[len("##REPORT##:"):])
                    all_reports.append(json_obj)
                except Exception as e:
                    print(f"[!] Failed to parse JSON: {e}")

            # 结束条件
            if "[Done]" in line:
                print("\n🎉 Test generation completed.\n")
                break

    # 返回结构化结果
    return all_reports




if __name__ == "__main__":
    ret = test_streaming_api()
    print("Structure Report:")
    print(json.dumps(ret, indent=2, ensure_ascii=False))