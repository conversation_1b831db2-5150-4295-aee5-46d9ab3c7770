from langchain_openai import ChatOpenAI
from langchain_ollama.llms import OllamaLLM
import os

# Agent 最大递归次数
RECURSION_LIMIT = 50

# LLM信息
# os.environ["OPENAI_API_BASE"] = "https://api.deepseek.com/v1"
# os.environ["OPENAI_API_KEY"] = "sk-58dd68ac267d494fac181f3007317a8f"
# llm = ChatOpenAI(model="deepseek-chat")

# llm = ChatOpenAI(
#     api_key="deepseek",
#     model="deepseek-r1:70b",
#     base_url="http://localhost:11434/v1",
# )

os.environ["OPENAI_API_BASE"] = "https://api.geekai.me/v1"
os.environ["OPENAI_API_KEY"] = "sk-b0i1SUYCvbKAbIxf6429AfF468D8480dAe3c1eB303C5C259"
llm = ChatOpenAI(model="gpt-4o-2024-08-06")
# llm = ChatOpenAI(model="gpt-4o")

# os.environ["OPENAI_API_BASE"] = "https://integrate.api.nvidia.com/v1"
# os.environ["OPENAI_API_KEY"] = "**********************************************************************"
# llm = ChatOpenAI(model="meta/llama-3.1-405b-instruct")

# LLM是否自带原生function call功能
Enable_Native_Function_Call = True

# 是否限制对测试用例相关内容进行检索
Limit_Retrieve_Test_Case = False

# CKGConstruction.jar路径
CKGConstruction_Jar_Path = "/Users/<USER>/Desktop/CKGConstruction/target/CKGConstruction-1.0-SNAPSHOT.jar"