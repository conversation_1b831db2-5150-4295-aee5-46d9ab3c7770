#!/usr/bin/env python3
"""
增强的测试生成客户端
支持WebSocket实时通信和任务管理
"""

import asyncio
import json
import requests
import websockets
from datetime import datetime
from typing import Dict, List, Optional


class TestAgentClient:
    """测试生成客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.ws_url = base_url.replace("http://", "ws://").replace("https://", "wss://")
    
    def create_task(self, method_id: int, url: str, username: str, password: str, 
                   limit: int = 50, base_path: Optional[str] = None) -> str:
        """创建新的测试生成任务"""
        payload = {
            "method_id": method_id,
            "url": url,
            "username": username,
            "password": password,
            "limit": limit
        }
        if base_path:
            payload["base_path"] = base_path
        
        response = requests.post(f"{self.base_url}/tasks", json=payload)
        response.raise_for_status()
        return response.json()["task_id"]
    
    def get_task_info(self, task_id: str) -> Dict:
        """获取任务信息"""
        response = requests.get(f"{self.base_url}/tasks/{task_id}")
        response.raise_for_status()
        return response.json()
    
    def get_task_progress(self, task_id: str) -> Dict:
        """获取任务进度"""
        response = requests.get(f"{self.base_url}/tasks/{task_id}/progress")
        response.raise_for_status()
        return response.json()
    
    def get_task_result(self, task_id: str) -> Dict:
        """获取任务结果"""
        response = requests.get(f"{self.base_url}/tasks/{task_id}/result")
        response.raise_for_status()
        return response.json()
    
    def list_tasks(self, limit: int = 50, offset: int = 0) -> List[Dict]:
        """获取任务列表"""
        response = requests.get(f"{self.base_url}/tasks", params={"limit": limit, "offset": offset})
        response.raise_for_status()
        return response.json()
    
    def cancel_task(self, task_id: str) -> Dict:
        """取消任务"""
        response = requests.delete(f"{self.base_url}/tasks/{task_id}")
        response.raise_for_status()
        return response.json()
    
    async def monitor_task_with_websocket(self, task_id: str, callback=None):
        """通过WebSocket监控任务进度"""
        uri = f"{self.ws_url}/ws/{task_id}"
        
        try:
            async with websockets.connect(uri) as websocket:
                print(f"🔗 Connected to WebSocket for task {task_id}")
                
                async for message in websocket:
                    try:
                        data = json.loads(message)
                        message_type = data.get("type")
                        
                        if callback:
                            callback(data)
                        else:
                            self._default_message_handler(data)
                        
                        # 如果任务完成或失败，退出监控
                        if message_type in ["result", "error"]:
                            break
                            
                    except json.JSONDecodeError:
                        print(f"❌ Failed to parse message: {message}")
                    except Exception as e:
                        print(f"❌ Error processing message: {e}")
                        
        except websockets.exceptions.ConnectionClosed:
            print("🔌 WebSocket connection closed")
        except Exception as e:
            print(f"❌ WebSocket error: {e}")
    
    def _default_message_handler(self, data: Dict):
        """默认消息处理器"""
        message_type = data.get("type")
        task_id = data.get("task_id")
        message_data = data.get("data", {})
        
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        if message_type == "progress":
            status = message_data.get("status", "unknown")
            message = message_data.get("message", "")
            print(f"[{timestamp}] 📊 Task {task_id}: {status} - {message}")
            
        elif message_type == "step_update":
            step_name = message_data.get("step_name", "unknown")
            status = message_data.get("status", "unknown")
            print(f"[{timestamp}] 🔄 Step: {step_name} -> {status}")
            
        elif message_type == "log":
            if "method_info" in message_data:
                method_info = message_data["method_info"]
                print(f"[{timestamp}] 📋 Method Info:")
                for key, value in method_info.items():
                    print(f"    {key}: {value}")
            elif "step" in message_data:
                step = message_data["step"]
                print(f"[{timestamp}] 📝 Step {step} output received")
            else:
                message = message_data.get("message", "")
                print(f"[{timestamp}] 📝 {message}")
                
        elif message_type == "result":
            test_cases = message_data.get("test_cases", [])
            summary = message_data.get("summary", {})
            print(f"[{timestamp}] ✅ Task completed!")
            print(f"    Generated {len(test_cases)} test cases")
            if summary:
                print(f"    Summary: {summary}")
                
        elif message_type == "error":
            error = message_data.get("error", "Unknown error")
            print(f"[{timestamp}] ❌ Task failed: {error}")
            
        else:
            print(f"[{timestamp}] 📨 Unknown message type: {message_type}")
    
    async def run_task_with_monitoring(self, method_id: int, url: str, username: str, 
                                     password: str, limit: int = 50, base_path: Optional[str] = None):
        """运行任务并实时监控"""
        print("🚀 Creating task...")
        task_id = self.create_task(method_id, url, username, password, limit, base_path)
        print(f"✅ Task created: {task_id}")
        
        print("🔍 Starting real-time monitoring...")
        await self.monitor_task_with_websocket(task_id)
        
        print("📊 Fetching final result...")
        try:
            result = self.get_task_result(task_id)
            return result
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 202:
                print("⏳ Task is still running, please check later")
                return None
            else:
                raise


async def main():
    """示例用法"""
    client = TestAgentClient()
    
    # 检查服务健康状态
    try:
        health = requests.get(f"{client.base_url}/health")
        print(f"🏥 Service health: {health.json()}")
    except Exception as e:
        print(f"❌ Service unavailable: {e}")
        return
    
    # 运行测试生成任务
    try:
        result = await client.run_task_with_monitoring(
            method_id=44777,
            url="bolt://localhost:7687",
            username="neo4j",
            password="123456",
            limit=50
        )
        
        if result:
            print("\n🎉 Final Result:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    asyncio.run(main())
