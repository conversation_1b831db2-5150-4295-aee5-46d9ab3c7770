#!/usr/bin/env python3
"""
测试新添加的端点是否正常工作
"""

import requests
import json

def test_generate_test_case_stream_java():
    """测试 /generate_test_case_stream_java 端点"""
    url = 'http://localhost:8000/generate_test_case_stream_java'
    
    # 请求体参数
    payload = {
        "method_id": 44777,
        "url": "bolt://localhost:7687",
        "username": "neo4j",
        "password": "123456",
        "limit": 50
    }
    
    print("🧪 测试 /generate_test_case_stream_java 端点...")
    print(f"📡 发送请求到: {url}")
    print(f"📦 请求参数: {json.dumps(payload, indent=2)}")
    
    try:
        # 发起 POST 请求
        response = requests.post(url, json=payload, timeout=10)
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📋 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ 端点响应正常!")
            print("📄 响应内容 (前500字符):")
            content = response.text[:500]
            print(content)
            if len(response.text) > 500:
                print("... (内容被截断)")
        else:
            print(f"❌ 端点响应异常: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")

def test_health_endpoint():
    """测试健康检查端点"""
    url = 'http://localhost:8000/health'
    
    print("\n🏥 测试健康检查端点...")
    
    try:
        response = requests.get(url, timeout=5)
        
        if response.status_code == 200:
            print("✅ 健康检查正常!")
            health_data = response.json()
            print(f"📊 健康状态: {json.dumps(health_data, indent=2)}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 健康检查请求失败: {e}")

def test_tasks_endpoint():
    """测试任务创建端点"""
    url = 'http://localhost:8000/tasks'
    
    payload = {
        "method_id": 44777,
        "url": "bolt://localhost:7687",
        "username": "neo4j",
        "password": "123456",
        "limit": 50
    }
    
    print("\n📋 测试任务创建端点...")
    
    try:
        response = requests.post(url, json=payload, timeout=10)
        
        if response.status_code == 200:
            print("✅ 任务创建成功!")
            task_data = response.json()
            print(f"📊 任务信息: {json.dumps(task_data, indent=2)}")
            return task_data.get('task_id')
        else:
            print(f"❌ 任务创建失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 任务创建请求失败: {e}")
    
    return None

if __name__ == "__main__":
    print("🚀 开始测试 TestAgent API 端点...")
    print("=" * 50)
    
    # 测试健康检查
    test_health_endpoint()
    
    # 测试任务创建
    task_id = test_tasks_endpoint()
    
    # 测试流式端点
    test_generate_test_case_stream_java()
    
    print("\n" + "=" * 50)
    print("🎯 测试完成!")
