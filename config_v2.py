"""
TestAgent v2.0 配置文件
包含系统的所有配置选项
"""

import os
from typing import Optional
from pydantic import BaseSettings, Field


class TestAgentSettings(BaseSettings):
    """TestAgent系统配置"""
    
    # 服务器配置
    host: str = Field(default="0.0.0.0", description="服务器地址")
    port: int = Field(default=8000, description="服务器端口")
    debug: bool = Field(default=False, description="调试模式")
    reload: bool = Field(default=False, description="自动重载")
    
    # 任务管理配置
    max_concurrent_tasks: int = Field(default=10, description="最大并发任务数")
    task_timeout: int = Field(default=3600, description="任务超时时间(秒)")
    task_cleanup_interval: int = Field(default=300, description="任务清理间隔(秒)")
    max_task_history: int = Field(default=1000, description="最大任务历史记录数")
    
    # WebSocket配置
    websocket_timeout: int = Field(default=30, description="WebSocket超时时间(秒)")
    websocket_ping_interval: int = Field(default=20, description="WebSocket心跳间隔(秒)")
    max_websocket_connections: int = Field(default=100, description="最大WebSocket连接数")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_dir: str = Field(default="log", description="日志目录")
    log_max_size: int = Field(default=10 * 1024 * 1024, description="单个日志文件最大大小(字节)")
    log_backup_count: int = Field(default=5, description="日志文件备份数量")
    
    # 结果存储配置
    result_dir: str = Field(default="result", description="结果存储目录")
    stream_dir: str = Field(default="result/stream", description="流式日志目录")
    report_dir: str = Field(default="result/report", description="报告存储目录")
    
    # Neo4j数据库配置
    neo4j_url: str = Field(default="bolt://localhost:7687", description="Neo4j连接URL")
    neo4j_username: str = Field(default="neo4j", description="Neo4j用户名")
    neo4j_password: str = Field(default="123456", description="Neo4j密码")
    neo4j_timeout: int = Field(default=30, description="Neo4j连接超时(秒)")
    
    # LLM配置
    openai_api_key: Optional[str] = Field(default=None, description="OpenAI API密钥")
    openai_base_url: Optional[str] = Field(default=None, description="OpenAI API基础URL")
    llm_model: str = Field(default="gpt-3.5-turbo", description="LLM模型名称")
    llm_temperature: float = Field(default=0.1, description="LLM温度参数")
    llm_max_tokens: int = Field(default=4000, description="LLM最大令牌数")
    llm_timeout: int = Field(default=60, description="LLM请求超时(秒)")
    
    # 测试生成配置
    default_recursion_limit: int = Field(default=50, description="默认递归限制")
    max_recursion_limit: int = Field(default=100, description="最大递归限制")
    default_max_feedback_times: int = Field(default=3, description="默认最大反馈次数")
    
    # 安全配置
    cors_origins: list = Field(default=["*"], description="CORS允许的源")
    cors_methods: list = Field(default=["*"], description="CORS允许的方法")
    cors_headers: list = Field(default=["*"], description="CORS允许的头部")
    
    # 性能配置
    worker_processes: int = Field(default=1, description="工作进程数")
    worker_connections: int = Field(default=1000, description="每个工作进程的连接数")
    keepalive_timeout: int = Field(default=5, description="Keep-Alive超时时间")
    
    # 监控配置
    enable_metrics: bool = Field(default=True, description="启用指标收集")
    metrics_port: int = Field(default=9090, description="指标服务端口")
    health_check_interval: int = Field(default=30, description="健康检查间隔(秒)")
    
    class Config:
        env_file = ".env"
        env_prefix = "TESTAGENT_"
        case_sensitive = False


class DevelopmentSettings(TestAgentSettings):
    """开发环境配置"""
    debug: bool = True
    reload: bool = True
    log_level: str = "DEBUG"
    max_concurrent_tasks: int = 5


class ProductionSettings(TestAgentSettings):
    """生产环境配置"""
    debug: bool = False
    reload: bool = False
    log_level: str = "INFO"
    worker_processes: int = 4
    max_concurrent_tasks: int = 20


class TestSettings(TestAgentSettings):
    """测试环境配置"""
    debug: bool = True
    log_level: str = "DEBUG"
    max_concurrent_tasks: int = 2
    task_timeout: int = 60
    neo4j_url: str = "bolt://localhost:7688"  # 测试数据库


def get_settings() -> TestAgentSettings:
    """获取配置实例"""
    env = os.getenv("TESTAGENT_ENV", "development").lower()
    
    if env == "production":
        return ProductionSettings()
    elif env == "test":
        return TestSettings()
    else:
        return DevelopmentSettings()


# 全局配置实例
settings = get_settings()


# 配置验证函数
def validate_settings():
    """验证配置的有效性"""
    errors = []
    
    # 验证端口范围
    if not (1 <= settings.port <= 65535):
        errors.append(f"端口号必须在1-65535范围内，当前值: {settings.port}")
    
    # 验证超时时间
    if settings.task_timeout <= 0:
        errors.append(f"任务超时时间必须大于0，当前值: {settings.task_timeout}")
    
    # 验证并发任务数
    if settings.max_concurrent_tasks <= 0:
        errors.append(f"最大并发任务数必须大于0，当前值: {settings.max_concurrent_tasks}")
    
    # 验证日志级别
    valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
    if settings.log_level.upper() not in valid_log_levels:
        errors.append(f"无效的日志级别: {settings.log_level}，有效值: {valid_log_levels}")
    
    # 验证LLM参数
    if not (0 <= settings.llm_temperature <= 2):
        errors.append(f"LLM温度参数必须在0-2范围内，当前值: {settings.llm_temperature}")
    
    if settings.llm_max_tokens <= 0:
        errors.append(f"LLM最大令牌数必须大于0，当前值: {settings.llm_max_tokens}")
    
    if errors:
        raise ValueError("配置验证失败:\n" + "\n".join(f"- {error}" for error in errors))
    
    return True


# 创建必要的目录
def create_directories():
    """创建配置中指定的目录"""
    directories = [
        settings.log_dir,
        settings.result_dir,
        settings.stream_dir,
        settings.report_dir
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)


if __name__ == "__main__":
    # 测试配置
    try:
        validate_settings()
        create_directories()
        print("✅ 配置验证通过")
        print(f"当前环境: {os.getenv('TESTAGENT_ENV', 'development')}")
        print(f"服务器地址: {settings.host}:{settings.port}")
        print(f"最大并发任务: {settings.max_concurrent_tasks}")
        print(f"日志级别: {settings.log_level}")
    except ValueError as e:
        print(f"❌ {e}")
    except Exception as e:
        print(f"❌ 配置错误: {e}")
