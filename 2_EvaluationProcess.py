import json
from CKGRetriever import CKGRetriever
from EnvironmentService import EnvironmentService
from LoggerManager import LoggerManager
from Evaluator import EvaluatorState
from Evaluator import evaluator_graph
from Config import *
import logging
from Utils import add_record, add_test_case

# 检索知识图谱中所有TestClass，并对test_report不为Success的TestClass进行修复
# 使用search_similarity_test_class工具检索知识图谱中相似的TestClass
# 该过程不涉及Coordinator，只在Executor中进行

config = {"recursion_limit": RECURSION_LIMIT}
graph_retriever = CKGRetriever("bolt://localhost:7687", "neo4j", "123456")
envServer = EnvironmentService()
# envServer.set_base_path("/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0")
envServer.set_base_path("/Users/<USER>/Desktop/java-project/commons-csv-rel-commons-csv-1.10.0")
# envServer.set_base_path("/Users/<USER>/Desktop/java-project/gson-gson-parent-2.12.0/gson")
# envServer.set_base_path("/Users/<USER>/Desktop/java-project/event-ruler-1.8.0")
# envServer.set_base_path("/Users/<USER>/Desktop/java-project/commons-lang-rel-commons-lang-3.10")
# envServer.set_base_path("/Users/<USER>/Desktop/java-project/jfreechart-1.5.5")
methods_list = graph_retriever.load_methods(5)
# if commons-lang and jfreechart, use this
# methods_list = graph_retriever.load_filtered_methods()
############### log setting ###############
generate_log_dir = "result/csv-0402-gpt_4o(woTool)"
log_dir = generate_log_dir + "(eval_CC)"
logger_manager = LoggerManager()
logger = logger_manager.logger
###########################################

# 跑该实验前的注意事项：
# 1. 请确保是紧接着GenerationProcess.py运行的，否则知识图谱内容可能对应不上
# 2. 请确保generate_log_dir存在且与generator的dir对应，log_dir不存在
# 3. 请确保Config中Enable_Native_Function_Call配置正确
# 4. 请确保Config中Limit_Retrieve_Test_Case配置正确（对于评估而言一般为False，消融实验可设置为True）
# 5. 请确保line 56中是否需要根据index跳过一些用例
# 6. 换项目时记得修改envServer.set_base_path

if not os.path.exists(log_dir):
    os.makedirs(log_dir)
generate_record_path = os.path.join(generate_log_dir, "record.jsonl")


def read_jsonl(file_path):
    data = []
    with open(file_path, "r") as file:
        for line in file:
            data.append(json.loads(line))
    return data


if __name__ == '__main__':
    generate_record = read_jsonl(generate_record_path)
    index = 0
    error_count = 0
    skip_count = 0
    max_retries = 2
    for method in methods_list:
        old_log_message = []
        retries = 0
        index += 1
        # if index not in [132, 152, 75, 165, 174]:
        #     continue
        record_key = f"{index}_{method.name}"
        record = None
        for item in generate_record:
            if item["name"] == record_key:
                record = item
                break
        if record and record["coverage"] == 1 and record["mutation_score"] == 1:
            # 已经获取完全覆盖和变异分数，跳过
            logger.info(f"{index}_{method.name} get full coverage and mutation score, Skip.")
            add_record(log_dir, record["name"], record["ret"], record["find_bugs"], record["coverage"],
                       record["mutation_score"],
                       record["line_coverage_rates"], record["branch_coverage_rates"], record["mutation_scores"],
                       record["total_lines"],
                       record["covered_lines"], record["mutation_info"], False)
            skip_count += 1
            continue

        if not os.path.exists(f"{log_dir}/detail/{str(index) + '_' + method.name}"):
            os.makedirs(f"{log_dir}/detail/{str(index) + '_' + method.name}")

        log_path = f"{log_dir}/detail/{str(index) + '_' + method.name}/{index}_{method.name}.log"
        logger_manager.change_log_path(log_path)
        logger.info(str(index) + '_' + method.name + " " + method.signature)
        test_clazz_list = graph_retriever.search_test_class_by_signature(method.signature, method.full_qualified_name)[:5]
        if isinstance(test_clazz_list, str) or record["ret"][0] == "FAIL":
            logger.error(f"search test class error: {test_clazz_list}, Skip.")
            error_count += 1
            add_record(log_dir, str(index) + '_' + method.name, ["FAIL"], [], 0, 0, [], [], [])
            continue

        while retries < max_retries:
            try:
                logger.info("===================== TEST EVALUATION START =====================\n")
                logger.info(f"method_name: {method.name}\n")
                logger.info(f"method_signature: {method.signature}\n")
                logger.info(f"absolute_path: {method.absolute_path}\n\n")
                logger.info(f"test_record: {json.dumps(record, indent=4)}\n")
                graph_retriever.change_focal_method_id(int(method.id))
                # inject_path = method.absolute_path.replace("src/main/java", "src/test/java")
                inject_path = method.absolute_path.replace("src/main", "src/test")
                inject_dir = inject_path[:inject_path.rfind("/")]
                envServer.set_inject_dir(inject_dir)
                clazz = graph_retriever.search_clazz_query(method.class_name, method.full_qualified_name.rsplit('.', 1)[0])

                state = EvaluatorState(envServer=envServer,
                                       package_name=method.package_name,
                                       method_id=int(method.id),
                                       method_code=method.content,
                                       method_signature=method.signature,
                                       start_line=method.start_line,
                                       end_line=method.end_line,
                                       class_name=method.class_name,
                                       full_method_name=method.full_qualified_name,
                                       method_summary=method.summarization if method.summarization else "",
                                       old_test_cases=test_clazz_list)

                for event in evaluator_graph.stream(state, config, subgraphs=True):
                    local_log_path = f"{log_dir}/detail/{str(index) + '_' + method.name}/{str(event[0])}.log"
                    local_handler = logging.FileHandler(local_log_path, mode='a')
                    logger_manager.add_file_handler(local_handler)
                    logger.info("-----Graph:" + str(event[0]) + "------")
                    logger.info("===================== Graph:" + str(event[0]) + " =====================\n")
                    logger.info("Current Node: " + str(list(event[1].keys())[0]) + "\n")
                    for value in event[1].values():
                        if value and 'messages' in value.keys():
                            for m in value["messages"]:
                                logger.info(m.pretty_repr() + '\n\n')
                        elif value and 'log_message' in value.keys() and old_log_message != value["log_message"]:
                            logger.info("-=-=-=-=-=-= Retry Log Below =-=-=-=-=-=-")
                            old_log_message = value["log_message"]
                            for m in value["log_message"]:
                                logger.info(m.pretty_repr() + '\n')
                            logger.info("-=-=-=-=-=-= Retry Log Above =-=-=-=-=-=-")
                        else:
                            logger.info('\n' + logger_manager.format_dict(value) + '\n')
                    logger_manager.remove_file_handler(local_handler)
                    event = event[1]
                    EndPoint = 'testCaseAcceptor'
                    if EndPoint in event.keys():
                        logger.info("===================== TEST GENERATE END =====================\n\n")
                        # 对生成的一批测试用例进行总结
                        result = []
                        test_code = []
                        find_bugs = []
                        covered_lines = set()
                        total_lines = set()
                        mutation_info = {}
                        line_coverage_rates = []
                        branch_coverage_rates = []
                        mutation_scores = []
                        mutants_list = []
                        test_point = []
                        test_report = []
                        inner_index = 0
                        for test_case in event[EndPoint]['test_cases']:
                            logger.info(f"test case: {LoggerManager.format_dict(test_case['test_case'])}\n")
                            inner_index += 1
                            logger.info(f"===================== Test Report {inner_index} =====================\n")
                            logger.info(f"test result: {LoggerManager.format_dict(test_case['test_result'])}\n")
                            logger.info(f"find bugs: {LoggerManager.format_dict(test_case['find_bug'])}\n")
                            logger.info(f"test case: {LoggerManager.format_dict(test_case['test_case'])}\n")
                            logger.info(f"test point: {LoggerManager.format_dict(test_case['requirement'])}\n")
                            logger.info(f"test report: {LoggerManager.format_dict(test_case['test_report'])}\n")
                            logger.info(f"coverage report: {LoggerManager.format_dict(test_case['coverage_report'])}\n")
                            logger.info(f"mutation report: {LoggerManager.format_dict(test_case['mutation_report'])}\n")
                            result.append(test_case['test_result'])
                            test_code.append(test_case['test_case'])
                            find_bugs.append(test_case['find_bug'])
                            test_point.append(test_case['requirement'])
                            test_report.append(test_case['test_report'])
                            if test_case['coverage_report']['result'] == 'Success':
                                line_coverage_rates.append(test_case['coverage_report']['output']['line_coverage'])
                                branch_coverage_rates.append(test_case['coverage_report']['output']['branch_coverage'])
                                total_lines.update(test_case['coverage_report']['output']['covered_lines'])
                                total_lines.update(test_case['coverage_report']['output']['missed_lines'])
                                covered_lines.update(test_case['coverage_report']['output']['covered_lines'])
                            else:
                                line_coverage_rates.append(0)
                                branch_coverage_rates.append(0)
                            if test_case['mutation_report']['result'] == 'Success':
                                mutation_scores.append(test_case['mutation_report']['output']['mutation_score'])
                                mutants_list.append(test_case['mutation_report']['output']['filtered_mutations'])
                                for item_mutation in test_case['mutation_report']['output']['filtered_mutations']:
                                    key = str(item_mutation['Line']) + '_' + str(item_mutation['Mutator'])
                                    if key in mutation_info.keys() and mutation_info[key] == 'KILLED':
                                        continue
                                    else:
                                        mutation_info[key] = item_mutation['Status']
                            else:
                                mutation_scores.append(0)

                        logger.info(f"test result: {LoggerManager.format_dict(result)}\n")

                        covered_lines.update(record["covered_lines"])
                        total_lines.update(record["total_lines"])
                        for key, status in record["mutation_info"].items():
                            if key not in mutation_info:
                                # 如果 mutation_info 中没有这个 key，则直接加入
                                mutation_info[key] = status
                            else:
                                # 如果 key 已存在，检查是否有 'KILLED'
                                if mutation_info[key] == "KILLED" or status == "KILLED":
                                    mutation_info[key] = "KILLED"
                                # 否则，不修改 mutation_info[key]
                        cal_coverage = len(covered_lines) / len(total_lines) if len(total_lines) != 0 else 0
                        final_coverage = cal_coverage if cal_coverage > max(line_coverage_rates) / 100 else max(line_coverage_rates) / 100
                        logger.info(f"final coverage: {final_coverage}\n")
                        final_mutation_score = sum([1 for v in mutation_info.values() if v == 'KILLED']) / len(
                            mutation_info) if len(mutation_info) != 0 else 0
                        logger.info(f"final mutation score: {final_mutation_score}\n")
                        if "Success" in record["ret"]:
                            result.append("Success")

                        add_record(log_dir, str(index) + '_' + method.name, result, find_bugs, final_coverage,
                                   final_mutation_score, line_coverage_rates, branch_coverage_rates, mutation_scores,
                                   total_lines, covered_lines, mutation_info, True)
                        add_test_case(log_dir, str(index) + '_' + method.name, find_bugs, test_point, test_code,
                                      method.signature, mutants_list)
                break

            except Exception as e:
                retries += 1
                logger.exception("===================== ERROR OCCUR =====================\n")

                if retries == max_retries:
                    logger.error(f"Max retries reached for method {method}. Skipping...\n")
                    add_record(log_dir, str(index) + '_' + method.name, ["FAIL"], [], 0, 0, [], [], [])

    logger.info(f"Skip count: {skip_count}")
    logger.info(f"Error count: {error_count}")
    logger.info(f"Total count: {len(methods_list)}")
