import logging
import colorama
from colorama import Fore, Style
import threading
import json

colorama.init(autoreset=True)  # 让颜色在每次日志输出后重置，避免影响后续文本


class ColoredFormatter(logging.Formatter):
    """自定义日志格式，给不同级别的日志着色"""

    LEVEL_COLORS = {
        logging.DEBUG: Fore.LIGHTBLACK_EX,  # 灰色
        logging.INFO: Fore.BLACK,  # 默认色（黑色/终端默认）
        logging.WARNING: Fore.YELLOW,  # 黄色
        logging.ERROR: Fore.RED,  # 红色
        logging.CRITICAL: Fore.LIGHTRED_EX  # 深红色
    }

    def format(self, record):
        log_color = self.LEVEL_COLORS.get(record.levelno, Style.RESET_ALL)
        log_message = super().format(record)
        return log_color + log_message + Style.RESET_ALL


class LoggerManager:
    """单例模式的 Logger 管理类"""

    _instance = None
    _lock = threading.Lock()  # 线程安全

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(LoggerManager, cls).__new__(cls)
                cls._instance._init_logger()
        return cls._instance

    def _init_logger(self):
        """初始化 Logger"""
        self.logger = logging.getLogger("logger")
        self.logger.setLevel(logging.DEBUG)

        # 创建 StreamHandler（输出到控制台）
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)
        formatter = ColoredFormatter("%(asctime)s - %(levelname)s - %(message)s")
        console_handler.setFormatter(formatter)

        self.logger.addHandler(console_handler)

    def change_log_path(self, log_path):
        """更改日志文件路径"""
        for handler in self.logger.handlers[:]:
            if isinstance(handler, logging.FileHandler):
                self.logger.removeHandler(handler)
                handler.close()
        file_handler = logging.FileHandler(log_path, mode='a')
        file_handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)

    def add_file_handler(self, file_handler):
        """添加文件处理器"""
        file_handler.setFormatter(logging.Formatter("%(asctime)s - %(levelname)s - %(message)s"))
        self.logger.addHandler(file_handler)

    def remove_file_handler(self, file_handler):
        """移除文件处理器"""
        self.logger.removeHandler(file_handler)
        file_handler.close()

    @staticmethod
    def format_dict(data):
        """
        判断输入变量是否为 dict，若是，则返回格式化的 JSON 字符串，否则返回普通字符串。

        :param data: 任何类型的数据
        :return: 若 data 是 dict，则返回 json.dumps 处理后的字符串；否则返回原始字符串
        """
        if isinstance(data, dict):
            return json.dumps(data, indent=4, ensure_ascii=False)
        return str(data)


if __name__ == "__main__":
    # 获取 Logger 实例
    logger_manager = LoggerManager()
    logger = logger_manager.logger

    # 记录不同级别的日志
    logger.debug("这是一条 DEBUG 日志（灰色）")
    logger.info("这是一条 INFO 日志（默认色）")
    logger.warning("这是一条 WARNING 日志（黄色）")
    logger.error("这是一条 ERROR 日志（红色）")
    logger.critical("这是一条 CRITICAL 日志（深红色）")