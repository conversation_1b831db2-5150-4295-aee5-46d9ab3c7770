# TestAgent v2.0 - 智能测试用例生成系统

## 🚀 重大更新

TestAgent v2.0 是对原有系统的完全重构，从用户体验角度出发，提供了更加直观、实时的测试用例生成体验。

### ✨ 新特性

- **🔄 实时进度追踪**: 可视化显示每个生成步骤的进度和状态
- **📡 WebSocket通信**: 双向实时通信，支持任务监控和中断
- **📋 任务管理系统**: 完整的任务生命周期管理
- **🎨 现代化UI**: 响应式Web界面，支持实时可视化
- **🔧 RESTful API**: 标准化的API接口设计
- **📊 详细的状态报告**: 每个步骤的详细输出和错误信息

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   FastAPI后端   │    │   LangGraph     │
│   (HTML/JS)     │◄──►│   (REST+WS)     │◄──►│   工作流引擎    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   实时监控      │    │   任务管理器    │    │   测试生成器    │
│   进度显示      │    │   状态持久化    │    │   结果评估      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📦 安装和配置

### 环境要求

- Python 3.8+
- FastAPI
- WebSockets
- LangGraph
- Neo4j数据库

### 安装依赖

```bash
pip install fastapi uvicorn websockets pydantic
# 其他依赖已在requirements.txt中定义
```

### 启动服务

```bash
# 启动FastAPI服务器
python main.py

# 或使用uvicorn
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

## 🔌 API接口

### 1. 创建任务

```http
POST /tasks
Content-Type: application/json

{
    "method_id": 44777,
    "url": "bolt://localhost:7687",
    "username": "neo4j",
    "password": "123456",
    "limit": 50,
    "base_path": "/optional/path"
}
```

**响应:**
```json
{
    "task_id": "uuid-string",
    "status": "created"
}
```

### 2. 获取任务信息

```http
GET /tasks/{task_id}
```

### 3. 获取任务进度

```http
GET /tasks/{task_id}/progress
```

### 4. 获取任务结果

```http
GET /tasks/{task_id}/result
```

### 5. 取消任务

```http
DELETE /tasks/{task_id}
```

### 6. WebSocket监控

```javascript
const ws = new WebSocket('ws://localhost:8000/ws/{task_id}');
ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    console.log('收到消息:', data);
};
```

## 🎯 使用方法

### 方法1: 使用Web界面

1. 打开 `demo.html` 文件
2. 填写测试参数
3. 点击"开始生成测试用例"
4. 实时查看进度和结果

### 方法2: 使用Python客户端

```python
import asyncio
from enhanced_client import TestAgentClient

async def main():
    client = TestAgentClient()
    
    # 创建并监控任务
    result = await client.run_task_with_monitoring(
        method_id=44777,
        url="bolt://localhost:7687",
        username="neo4j",
        password="123456",
        limit=50
    )
    
    print("生成结果:", result)

asyncio.run(main())
```

### 方法3: 直接API调用

```bash
# 创建任务
curl -X POST "http://localhost:8000/tasks" \
     -H "Content-Type: application/json" \
     -d '{
       "method_id": 44777,
       "url": "bolt://localhost:7687",
       "username": "neo4j",
       "password": "123456",
       "limit": 50
     }'

# 获取任务进度
curl "http://localhost:8000/tasks/{task_id}/progress"

# 获取任务结果
curl "http://localhost:8000/tasks/{task_id}/result"
```

## 📊 工作流步骤

系统包含以下主要步骤，每个步骤都有实时状态更新：

1. **initState** - 初始化状态
2. **methodAnalyzer** - 方法分析
3. **testPointsGenerator** - 测试点生成
4. **testCaseGenerator** - 测试用例生成
5. **testCaseAcceptor** - 测试用例验收
6. **reportGeneration** - 报告生成

## 🔍 监控和调试

### WebSocket消息类型

- `progress`: 总体进度更新
- `step_update`: 步骤状态更新
- `log`: 日志信息
- `result`: 最终结果
- `error`: 错误信息

### 日志文件

- 总日志: `result/stream/{method_id}_{method_name}_total.log`
- 步骤日志: `result/stream/{method_id}_{method_name}_{step}.log`
- 结果文件: `result/report/{method_id}_{method_name}_test_case.json`

## 🎨 前端可视化特性

- **实时进度条**: 显示任务完成百分比
- **步骤状态图标**: 每个步骤的状态可视化
- **实时日志流**: 彩色编码的日志输出
- **结果展示**: 格式化的测试用例显示
- **任务管理**: 创建、监控、取消任务

## 🔧 配置选项

### 任务配置

- `method_id`: 目标方法ID
- `url`: Neo4j数据库连接URL
- `username/password`: 数据库认证
- `limit`: 递归限制
- `base_path`: 可选的基础路径

### 系统配置

- `max_concurrent_tasks`: 最大并发任务数
- `task_timeout`: 任务超时时间
- `websocket_timeout`: WebSocket超时时间

## 🚨 错误处理

系统提供详细的错误信息和恢复机制：

- **连接错误**: 自动重连机制
- **任务失败**: 详细错误报告
- **超时处理**: 优雅的超时处理
- **资源清理**: 自动清理断开的连接

## 📈 性能优化

- **异步处理**: 全异步架构
- **连接池**: WebSocket连接管理
- **内存优化**: 及时清理完成的任务
- **并发控制**: 限制并发任务数量

## 🔮 未来计划

- [ ] 支持任务队列优先级
- [ ] 添加任务调度功能
- [ ] 集成更多测试框架
- [ ] 支持分布式部署
- [ ] 添加用户认证系统
- [ ] 提供更多可视化图表

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进系统！

## 📄 许可证

本项目采用MIT许可证。
