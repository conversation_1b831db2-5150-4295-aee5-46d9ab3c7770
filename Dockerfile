# TestAgent v2.0 Docker配置
FROM python:3.10-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV TESTAGENT_ENV=production
ENV PYTHONUNBUFFERED=1

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements_v2.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements_v2.txt

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p log result/stream result/report

# 设置权限
RUN chmod +x start_server.py

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["python", "start_server.py", "--host", "0.0.0.0", "--port", "8000"]
