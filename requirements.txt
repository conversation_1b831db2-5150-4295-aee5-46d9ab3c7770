ace_tools==0.0
aiohappyeyeballs==2.4.6
aiohttp==3.11.12
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.9.0
appdirs==1.4.4
appnope @ file:///home/<USER>/feedstock_root/build_artifacts/appnope_1707233003401/work
asttokens @ file:///home/<USER>/feedstock_root/build_artifacts/asttokens_1698341106958/work
async-timeout==4.0.3
attrs==25.1.0
baml-py==0.75.0
Bottleneck @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_55txi4fy1u/croot/bottleneck_1731058642212/work
Brotli @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_f7i0oxypt6/croot/brotli-split_1736182464088/work
cached-property==2.0.1
certifi==2025.1.31
chardet @ file:///Users/<USER>/ci_310/chardet_1643965356347/work
charset-normalizer==3.4.1
click==8.1.8
colorama==0.4.6
comm @ file:///home/<USER>/feedstock_root/build_artifacts/comm_1710320294760/work
configparser==7.2.0
contourpy @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_2cvjf0v4ux/croot/contourpy_1732540055997/work
cramjam==2.10.0
cycler @ file:///tmp/build/80754af9/cycler_1637851556182/work
dataclasses-json==0.6.7
datasets==3.5.0
debugpy @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_6a37he2v_t/croot/debugpy_1736267437603/work
decorator @ file:///home/<USER>/feedstock_root/build_artifacts/decorator_1641555617451/work
diff_cover==9.2.4
dill==0.3.8
distro==1.9.0
exceptiongroup==1.2.2
executing @ file:///home/<USER>/feedstock_root/build_artifacts/executing_1698579936712/work
fastapi==0.115.12
fastparquet==2024.11.0
filelock==3.18.0
fonttools @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_ce1jt_55vl/croot/fonttools_1737039388732/work
frozenlist==1.5.0
fsspec==2024.12.0
grpcio @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_d2dk367etj/croot/grpc-split_1716834577849/work
grpcio-tools @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_6b0m18be35/croot/grpcio-tools_1721915941870/work
h11==0.14.0
h5py==3.13.0
httpcore==1.0.7
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.30.2
hypothesis==6.130.12
idna==3.10
importlib_metadata @ file:///home/<USER>/feedstock_root/build_artifacts/importlib-metadata_1710971335535/work
iniconfig==2.1.0
ipykernel @ file:///Users/<USER>/miniforge3/conda-bld/ipykernel_1717717537056/work
ipython @ file:///home/<USER>/feedstock_root/build_artifacts/ipython_1701831663892/work
javalang==0.13.0
jedi @ file:///home/<USER>/feedstock_root/build_artifacts/jedi_1696326070614/work
Jinja2==3.1.6
jiter==0.8.2
joblib==1.4.2
jsonpatch==1.33
jsonpointer==3.0.0
jupyter_client @ file:///home/<USER>/feedstock_root/build_artifacts/jupyter_client_1716472197302/work
jupyter_core @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_73nomeum4p/croot/jupyter_core_1718818302815/work
kiwisolver @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_cc2l_z_0ri/croot/kiwisolver_1737039586949/work
langchain==0.3.18
langchain-community==0.3.17
langchain-core==0.3.45
langchain-ollama==0.2.3
langchain-openai==0.3.4
langchain-text-splitters==0.3.6
langgraph==0.3.11
langgraph-checkpoint==2.0.20
langgraph-prebuilt==0.1.3
langgraph-sdk==0.1.57
langsmith==0.3.15
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib==3.10.0
matplotlib-inline @ file:///home/<USER>/feedstock_root/build_artifacts/matplotlib-inline_1713250518406/work
msgpack==1.1.0
multidict==6.1.0
multiprocess==0.70.16
mypy-extensions==1.0.0
neo4j==5.28.1
nest_asyncio @ file:///home/<USER>/feedstock_root/build_artifacts/nest-asyncio_1705850609492/work
networkx==3.4.2
numexpr @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_b3kvvt6tc6/croot/numexpr_1730215947700/work
numpy==1.26.4
ollama==0.4.7
openai==1.61.1
orjson==3.10.15
oyaml==1.0
packaging @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_a6_qk3qyg7/croot/packaging_1734472142254/work
pandas @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_4aifrweohv/croot/pandas_1732735109535/work/dist/pandas-2.2.3-cp310-cp310-macosx_11_0_arm64.whl#sha256=6c5bbde94d3ac89a919939bdecfabca704888cdaf6251a6b101db7024fed27ab
parso @ file:///home/<USER>/feedstock_root/build_artifacts/parso_1712320355065/work
pathspec==0.12.1
pexpect @ file:///home/<USER>/feedstock_root/build_artifacts/pexpect_1706113125309/work
pickleshare @ file:///home/<USER>/feedstock_root/build_artifacts/pickleshare_1602536217715/work
pillow @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_85xj2lenf1/croot/pillow_1738010251686/work
platformdirs @ file:///home/<USER>/feedstock_root/build_artifacts/platformdirs_1715777629804/work
pluggy==1.5.0
prompt_toolkit @ file:///home/<USER>/feedstock_root/build_artifacts/prompt-toolkit_1718047967974/work
propcache==0.2.1
protobuf==4.25.3
psutil @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_10oa1k8l11/croot/psutil_1736367646006/work
ptyprocess @ file:///home/<USER>/feedstock_root/build_artifacts/ptyprocess_1609419310487/work/dist/ptyprocess-0.7.0-py2.py3-none-any.whl
pure-eval @ file:///home/<USER>/feedstock_root/build_artifacts/pure_eval_1642875951954/work
-e git+https://github.com/pvlib/pvlib-python.git@04a523fafbd61bc2e49420963b84ed8e2bd1b3cf#egg=pvlib
pyarrow==19.0.1
pydantic==2.10.6
pydantic-settings==2.7.1
pydantic_core==2.27.2
Pygments==2.19.1
pyparsing @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_65qfw6vkxg/croot/pyparsing_1731445528142/work
PySocks @ file:///Users/<USER>/ci_310/pysocks_1643961536721/work
python-dateutil @ file:///home/<USER>/feedstock_root/build_artifacts/python-dateutil_1709299778482/work
python-dotenv==1.0.1
python-socks==2.7.1
pytz @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_a4b76c83ik/croot/pytz_1713974318928/work
PyYAML==6.0.2
pyzmq @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_95lsut8ymz/croot/pyzmq_1734709560733/work
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
scikit-learn==1.6.1
scipy==1.15.2
seaborn @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_818t92q9h3/croot/seaborn_1741271242303/work
six @ file:///home/<USER>/feedstock_root/build_artifacts/six_1620240208055/work
sniffio==1.3.1
socksio==1.0.0
sortedcontainers==2.4.0
SQLAlchemy==2.0.38
-e git+https://github.com/sqlfluff/sqlfluff.git@14e1a23a3166b9a645a16de96f694c77a5d4abb7#egg=sqlfluff
sseclient-py==1.8.0
stack-data @ file:///home/<USER>/feedstock_root/build_artifacts/stack_data_1669632077133/work
starlette==0.46.2
tblib==3.1.0
tenacity==9.0.0
threadpoolctl==3.6.0
tiktoken==0.8.0
toml==0.10.2
tomli==2.2.1
tornado @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_0axef5a0m0/croot/tornado_1733960501260/work
tqdm==4.67.1
traitlets @ file:///home/<USER>/feedstock_root/build_artifacts/traitlets_1713535121073/work
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata @ file:///croot/python-tzdata_1690578112552/work
ujson @ file:///private/var/folders/nz/j6p8yfhx1mv_0grj5xl4650h0000gp/T/abs_bb79980w_r/croot/ujson_1736541985924/work
unicodedata2 @ file:///private/var/folders/k1/30mswbxs7r1g6zwn8y4fyt500000gp/T/abs_b99hn8t9lq/croot/unicodedata2_1736541774279/work
urllib3==2.3.0
uvicorn==0.34.1
wcwidth @ file:///home/<USER>/feedstock_root/build_artifacts/wcwidth_1704731205417/work
xxhash==3.5.0
yarl==1.18.3
zipp @ file:///home/<USER>/feedstock_root/build_artifacts/zipp_1718013267051/work
zstandard==0.23.0
