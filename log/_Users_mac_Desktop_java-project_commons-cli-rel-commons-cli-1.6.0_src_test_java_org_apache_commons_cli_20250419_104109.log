Using the following parameters:
Project Path: /Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli
Neo4j URL: bolt://localhost:7687
Username: neo4j
Password: 123456
Include test files: true
Continual Build: true
=============================== Graph Construction ===============================
=========================== Starting graph construction ===========================
Find 1 Java files.
Continual build is enabled. Keep existing nodes and relationships.
================================ Building Nodes ==================================
Building nodes in file: _4_AlreadySelectedExceptionTest.java
Node created: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.setUp', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/_4_AlreadySelectedExceptionTest.java'}
Node created: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.testGetOptionReflectsModification', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/_4_AlreadySelectedExceptionTest.java'}
Node created: Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.exception', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/_4_AlreadySelectedExceptionTest.java'}
Node created: Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.option1', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/_4_AlreadySelectedExceptionTest.java'}
Node created: Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.option2', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/_4_AlreadySelectedExceptionTest.java'}
Node created: Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.optionGroup', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/_4_AlreadySelectedExceptionTest.java'}
Node created: TestClazz{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/_4_AlreadySelectedExceptionTest.java'}
All nodes have been created in the database.
All nodes have been created.
================================ Reading Nodes ==================================
Update all nodes.
================================ Building Edges ==================================
Relationship created: TestClazz{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest', absolute_path=''} ----CONTAIN---> Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.exception', absolute_path=''}
Relationship created: Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.exception', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli.AlreadySelectedException', absolute_path=''}
Relationship created: TestClazz{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest', absolute_path=''} ----CONTAIN---> Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.option1', absolute_path=''}
Relationship created: Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.option1', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli.Option', absolute_path=''}
Relationship created: TestClazz{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest', absolute_path=''} ----CONTAIN---> Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.option2', absolute_path=''}
Relationship created: Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.option2', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli.Option', absolute_path=''}
Relationship created: TestClazz{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest', absolute_path=''} ----CONTAIN---> Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.optionGroup', absolute_path=''}
Relationship created: Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.optionGroup', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli.OptionGroup', absolute_path=''}
Relationship created: TestClazz{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.setUp', absolute_path=''}
Relationship created: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.option1', absolute_path=''}
Relationship created: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.Option.Option', absolute_path=''}
Relationship created: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.option2', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.Option.Option', absolute_path=''}
Relationship created: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.optionGroup', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> null
Relationship created: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.OptionGroup.addOption', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.optionGroup', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.option1', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.OptionGroup.addOption', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.optionGroup', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.option2', absolute_path=''}
Relationship created: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.exception', absolute_path=''}
Relationship created: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.AlreadySelectedException.AlreadySelectedException', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.optionGroup', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.option1', absolute_path=''}
Relationship created: TestClazz{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.testGetOptionReflectsModification', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.testGetOptionReflectsModification', absolute_path=''} ----INVOKE---> null
Relationship created: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.testGetOptionReflectsModification', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.option1', absolute_path=''}
Relationship created: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.testGetOptionReflectsModification', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.AlreadySelectedException.getOption', absolute_path=''}
Relationship created: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.testGetOptionReflectsModification', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.exception', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.testGetOptionReflectsModification', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.exception', absolute_path=''}
Relationship created: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.testGetOptionReflectsModification', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.AlreadySelectedException.AlreadySelectedException', absolute_path=''}
Relationship created: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.testGetOptionReflectsModification', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.optionGroup', absolute_path=''}
Relationship created: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.testGetOptionReflectsModification', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.option2', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.testGetOptionReflectsModification', absolute_path=''} ----INVOKE---> null
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.testGetOptionReflectsModification', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.option2', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.testGetOptionReflectsModification', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.AlreadySelectedException.getOption', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.testGetOptionReflectsModification', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest.exception', absolute_path=''}
Target node does not exist: TestClazz{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest._4_AlreadySelectedExceptionTest', absolute_path=''}
Source node does not exist: Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest._4_AlreadySelectedExceptionTest', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._4_AlreadySelectedExceptionTest._4_AlreadySelectedExceptionTest', absolute_path=''} ----INVOKE---> null
All edges have been created in the database.
All edges have been created.
================================ Updating Nodes ==================================
All test nodes have been updated.
================================== Graph Summary ==================================
Nodes created: 7 Success, 
0 Fail
Edges created:
	23 SUCCESS,
	12 RELATIONSHIP_EXIST,
	6 NODE_NOT_EXIST,
recognized 41 edges
Nodes updated:
	1 UPDATED,
	6 SKIPPED,
Graph construction completed.
Total time cost: 0h 0m 1s
