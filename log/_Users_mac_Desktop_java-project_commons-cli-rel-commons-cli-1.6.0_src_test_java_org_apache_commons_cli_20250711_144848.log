Using the following parameters:
Project Path: /Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli
Neo4j URL: bolt://localhost:7687
Username: neo4j
Password: 123456
Include test files: true
Continual Build: true
=============================== Graph Construction ===============================
=========================== Starting graph construction ===========================
Find 3 Java files.
Continual build is enabled. Keep existing nodes and relationships.
================================ Building Nodes ==================================
Building nodes in file: _3_OptionBuilderTest.java
Building nodes in file: _2_OptionBuilderTest.java
Building nodes in file: _3_AlreadySelectedExceptionTest.java
Node already exists: Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.testCreateOptionWithLongOptSet', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_2_OptionBuilderTest.java'}
Node already exists: Method{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest.testCreateOptionWithMinimalLongOpt', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_3_OptionBuilderTest.java'}
Node already exists: TestClazz{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_2_OptionBuilderTest.java'}
Node already exists: TestClazz{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_3_OptionBuilderTest.java'}
Node created: Method{full_qualified_name='org.apache.commons.cli._3_AlreadySelectedExceptionTest.testGetOption_whenOptionIsNull', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/_3_AlreadySelectedExceptionTest.java'}
Node created: TestClazz{full_qualified_name='org.apache.commons.cli._3_AlreadySelectedExceptionTest', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/_3_AlreadySelectedExceptionTest.java'}
All nodes have been created in the database.
All nodes have been created.
================================ Reading Nodes ==================================
Update all nodes.
================================ Building Edges ==================================
Relationship created: TestClazz{full_qualified_name='org.apache.commons.cli._3_AlreadySelectedExceptionTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._3_AlreadySelectedExceptionTest.testGetOption_whenOptionIsNull', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._3_AlreadySelectedExceptionTest.testGetOption_whenOptionIsNull', absolute_path=''} ----INVOKE---> null
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.testCreateOptionWithLongOptSet', absolute_path=''}
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest.testCreateOptionWithMinimalLongOpt', absolute_path=''}
Target node does not exist: Method{full_qualified_name='org.apache.commons.cli._3_AlreadySelectedExceptionTest.testGetOption_whenOptionIsNull', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli.OptionGroup.class', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._3_AlreadySelectedExceptionTest.testGetOption_whenOptionIsNull', absolute_path=''} ----INVOKE---> null
Target node does not exist: Method{full_qualified_name='org.apache.commons.cli._3_AlreadySelectedExceptionTest.testGetOption_whenOptionIsNull', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli.AlreadySelectedException.class', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._3_AlreadySelectedExceptionTest.testGetOption_whenOptionIsNull', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._3_AlreadySelectedExceptionTest.testGetOption_whenOptionIsNull', absolute_path=''} ----INVOKE---> null
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.testCreateOptionWithLongOptSet', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.OptionBuilder.withLongOpt', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest.testCreateOptionWithMinimalLongOpt', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.OptionBuilder.withLongOpt', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.testCreateOptionWithLongOptSet', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.OptionBuilder.create', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.testCreateOptionWithLongOptSet', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.testCreateOptionWithLongOptSet', absolute_path=''} ----INVOKE---> null
Relationship created: Method{full_qualified_name='org.apache.commons.cli._3_AlreadySelectedExceptionTest.testGetOption_whenOptionIsNull', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.AlreadySelectedException.getOption', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.testCreateOptionWithLongOptSet', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.Option.getLongOpt', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest.testCreateOptionWithMinimalLongOpt', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.OptionBuilder.create', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest.testCreateOptionWithMinimalLongOpt', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest.testCreateOptionWithMinimalLongOpt', absolute_path=''} ----INVOKE---> null
Target node does not exist: TestClazz{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest._2_OptionBuilderTest', absolute_path=''}
Source node does not exist: Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest._2_OptionBuilderTest', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest._2_OptionBuilderTest', absolute_path=''} ----INVOKE---> null
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._3_AlreadySelectedExceptionTest.testGetOption_whenOptionIsNull', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.AlreadySelectedException.getOption', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._3_AlreadySelectedExceptionTest.testGetOption_whenOptionIsNull', absolute_path=''} ----INVOKE---> null
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest.testCreateOptionWithMinimalLongOpt', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.Option.getLongOpt', absolute_path=''}
Target node does not exist: TestClazz{full_qualified_name='org.apache.commons.cli._3_AlreadySelectedExceptionTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._3_AlreadySelectedExceptionTest._3_AlreadySelectedExceptionTest', absolute_path=''}
Target node does not exist: TestClazz{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest._3_OptionBuilderTest', absolute_path=''}
Source node does not exist: Method{full_qualified_name='org.apache.commons.cli._3_AlreadySelectedExceptionTest._3_AlreadySelectedExceptionTest', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli._3_AlreadySelectedExceptionTest', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._3_AlreadySelectedExceptionTest._3_AlreadySelectedExceptionTest', absolute_path=''} ----INVOKE---> null
Source node does not exist: Method{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest._3_OptionBuilderTest', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest._3_OptionBuilderTest', absolute_path=''} ----INVOKE---> null
All edges have been created in the database.
All edges have been created.
================================ Updating Nodes ==================================
All test nodes have been updated.
================================== Graph Summary ==================================
Nodes created: 2 Success, 
4 Fail
Edges created:
	2 SUCCESS,
	9 RELATIONSHIP_EXIST,
	20 NODE_NOT_EXIST,
recognized 31 edges
Nodes updated:
	1 UPDATED,
	5 SKIPPED,
Graph construction completed.
Total time cost: 0h 0m 1s
