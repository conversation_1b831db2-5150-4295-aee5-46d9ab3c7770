Using the following parameters:
Project Path: /Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli
Neo4j URL: bolt://localhost:7687
Username: neo4j
Password: 123456
Include test files: true
Continual Build: true
=============================== Graph Construction ===============================
=========================== Starting graph construction ===========================
Find 1 Java files.
Continual build is enabled. Keep existing nodes and relationships.
================================ Building Nodes ==================================
Building nodes in file: _1_TypeHandlerTest.java
Node created: Method{full_qualified_name='org.apache.commons.cli._1_TypeHandlerTest.testCreateFilesWithNullInput', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/_1_TypeHandlerTest.java'}
Node already exists: TestClazz{full_qualified_name='org.apache.commons.cli._1_TypeHandlerTest', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/_1_TypeHandlerTest.java'}
All nodes have been created in the database.
All nodes have been created.
================================ Reading Nodes ==================================
Update all nodes.
================================ Building Edges ==================================
Relationship created: TestClazz{full_qualified_name='org.apache.commons.cli._1_TypeHandlerTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._1_TypeHandlerTest.testCreateFilesWithNullInput', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._1_TypeHandlerTest.testCreateFilesWithNullInput', absolute_path=''} ----INVOKE---> null
Relationship created: Method{full_qualified_name='org.apache.commons.cli._1_TypeHandlerTest.testCreateFilesWithNullInput', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.TypeHandler.createFiles', absolute_path=''}
Target node does not exist: TestClazz{full_qualified_name='org.apache.commons.cli._1_TypeHandlerTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._1_TypeHandlerTest._1_TypeHandlerTest', absolute_path=''}
Source node does not exist: Method{full_qualified_name='org.apache.commons.cli._1_TypeHandlerTest._1_TypeHandlerTest', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli._1_TypeHandlerTest', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._1_TypeHandlerTest._1_TypeHandlerTest', absolute_path=''} ----INVOKE---> null
All edges have been created in the database.
All edges have been created.
================================ Updating Nodes ==================================
All test nodes have been updated.
================================== Graph Summary ==================================
Nodes created: 1 Success, 
1 Fail
Edges created:
	2 SUCCESS,
	0 RELATIONSHIP_EXIST,
	4 NODE_NOT_EXIST,
recognized 6 edges
Nodes updated:
	0 UPDATED,
	12 SKIPPED,
Graph construction completed.
Total time cost: 0h 0m 0s
