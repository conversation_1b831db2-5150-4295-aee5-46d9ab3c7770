Using the following parameters:
Project Path: /Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli
Neo4j URL: bolt://localhost:7687
Username: neo4j
Password: 123456
Include test files: true
Continual Build: true
=============================== Graph Construction ===============================
=========================== Starting graph construction ===========================
Find 1 Java files.
Continual build is enabled. Keep existing nodes and relationships.
================================ Building Nodes ==================================
Building nodes in file: _3_MissingOptionExceptionTest.java
Node created: Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.setUp', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/_3_MissingOptionExceptionTest.java'}
Node created: Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/_3_MissingOptionExceptionTest.java'}
Node created: Field{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.missingOptionException', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/_3_MissingOptionExceptionTest.java'}
Node created: TestClazz{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/_3_MissingOptionExceptionTest.java'}
All nodes have been created in the database.
All nodes have been created.
================================ Reading Nodes ==================================
Update all nodes.
================================ Building Edges ==================================
Relationship created: TestClazz{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest', absolute_path=''} ----CONTAIN---> Field{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.missingOptionException', absolute_path=''}
Relationship created: Field{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.missingOptionException', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli.MissingOptionException', absolute_path=''}
Relationship created: TestClazz{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.setUp', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.setUp', absolute_path=''} ----INVOKE---> null
Relationship created: Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.setUp', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.OptionGroup.addOption', absolute_path=''}
Relationship created: Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.setUp', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.Option$Builder.build', absolute_path=''}
Relationship created: Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.setUp', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.Option$Builder.longOpt', absolute_path=''}
Relationship created: Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.setUp', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.Option.builder', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.setUp', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.setUp', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.setUp', absolute_path=''} ----INVOKE---> null
Relationship created: Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.setUp', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.missingOptionException', absolute_path=''}
Relationship created: Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.setUp', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.MissingOptionException.MissingOptionException', absolute_path=''}
Relationship created: TestClazz{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''} ----INVOKE---> null
Relationship created: Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.OptionGroup.addOption', absolute_path=''}
Relationship created: Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.Option$Builder.build', absolute_path=''}
Relationship created: Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.Option$Builder.longOpt', absolute_path=''}
Relationship created: Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.Option.builder', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''} ----INVOKE---> null
Relationship created: Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.MissingOptionException.getMissingOptions', absolute_path=''}
Relationship created: Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.missingOptionException', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''} ----INVOKE---> null
Relationship created: Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.OptionGroup.getOptions', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.OptionGroup.getOptions', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''} ----INVOKE---> null
Relationship created: Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.Option.getOpt', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest.testGetMissingOptions_ShouldReturnCorrectMissingOptionsList', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.Option.getOpt', absolute_path=''}
Target node does not exist: TestClazz{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest._3_MissingOptionExceptionTest', absolute_path=''}
Source node does not exist: Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest._3_MissingOptionExceptionTest', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._3_MissingOptionExceptionTest._3_MissingOptionExceptionTest', absolute_path=''} ----INVOKE---> null
All edges have been created in the database.
All edges have been created.
================================ Updating Nodes ==================================
All test nodes have been updated.
================================== Graph Summary ==================================
Nodes created: 4 Success, 
0 Fail
Edges created:
	18 SUCCESS,
	2 RELATIONSHIP_EXIST,
	22 NODE_NOT_EXIST,
recognized 42 edges
Nodes updated:
	1 UPDATED,
	2 SKIPPED,
Graph construction completed.
Total time cost: 0h 0m 1s
