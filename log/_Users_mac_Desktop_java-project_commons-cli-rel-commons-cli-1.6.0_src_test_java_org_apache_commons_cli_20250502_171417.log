Using the following parameters:
Project Path: /Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli
Neo4j URL: bolt://localhost:7687
Username: neo4j
Password: 123456
Include test files: true
Continual Build: true
=============================== Graph Construction ===============================
=========================== Starting graph construction ===========================
Find 9 Java files.
Continual build is enabled. Keep existing nodes and relationships.
================================ Building Nodes ==================================
Building nodes in file: _4_TypeHandlerTest.java
Building nodes in file: _4_TypeHandlerTest.java
Building nodes in file: _1_TypeHandlerTest.java
Building nodes in file: _2_OptionBuilderTest.java
Building nodes in file: _3_TypeHandlerTest.java
Building nodes in file: _2_AlreadySelectedExceptionTest.java
Building nodes in file: _1_OptionBuilderTest.java
Building nodes in file: _2_TypeHandlerTest.java
Node already exists: Method{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest.setUp', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_1_OptionBuilderTest.java'}
Node already exists: Method{full_qualified_name='org.apache.commons.cli._2_TypeHandlerTest.testCreateDateUnsupportedOperation', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_2_TypeHandlerTest.java'}
Node already exists: Method{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest.testCreateDateWithEmptyString', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_4_TypeHandlerTest.java'}
Node already exists: Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.setUp', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_2_OptionBuilderTest.java'}
Node already exists: Method{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest.setUp', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_3_TypeHandlerTest.java'}
Node already exists: Method{full_qualified_name='org.apache.commons.cli._2_AlreadySelectedExceptionTest.testGetOptionGroup_whenGroupIsUninitialized_shouldReturnNull', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_2_AlreadySelectedExceptionTest.java'}
Node already exists: Method{full_qualified_name='org.apache.commons.cli._1_TypeHandlerTest.testCreateDateWithInvalidDateString', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_1_TypeHandlerTest.java'}
Node already exists: Method{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest.testCreateWithValidLongOption', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_1_OptionBuilderTest.java'}
Node already exists: TestClazz{full_qualified_name='org.apache.commons.cli._1_TypeHandlerTest', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_1_TypeHandlerTest.java'}
Node already exists: TestClazz{full_qualified_name='org.apache.commons.cli._2_AlreadySelectedExceptionTest', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_2_AlreadySelectedExceptionTest.java'}
Node already exists: TestClazz{full_qualified_name='org.apache.commons.cli._2_TypeHandlerTest', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_2_TypeHandlerTest.java'}
Node already exists: Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.testCreateThrowsIllegalArgumentExceptionWhenLongOptionIsNull', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_2_OptionBuilderTest.java'}
Node already exists: Method{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest.testCreateDate_validDateString', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_3_TypeHandlerTest.java'}
Node created: Method{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest.testCreateFilesWithEmptyString', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/_4_TypeHandlerTest.java'}
Node already exists: TestClazz{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_4_TypeHandlerTest.java'}
Building nodes in file: _1_AlreadySelectedExceptionTest.java
Node already exists: Field{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest.typeHandler', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_3_TypeHandlerTest.java'}
Node already exists: TestClazz{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_1_OptionBuilderTest.java'}
Node already exists: TestClazz{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_2_OptionBuilderTest.java'}
Node already exists: TestClazz{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/_4_TypeHandlerTest.java'}
Node already exists: TestClazz{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_3_TypeHandlerTest.java'}
Node already exists: Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_1_AlreadySelectedExceptionTest.java'}
Node already exists: Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.testGetOptionGroup_withExternallyManipulatedGroup', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_1_AlreadySelectedExceptionTest.java'}
Node already exists: Field{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.exception', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_1_AlreadySelectedExceptionTest.java'}
Node already exists: TestClazz{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_1_AlreadySelectedExceptionTest.java'}
All nodes have been created in the database.
All nodes have been created.
================================ Reading Nodes ==================================
Update all nodes.
================================ Building Edges ==================================
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.setUp', absolute_path=''}
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._2_TypeHandlerTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._2_TypeHandlerTest.testCreateDateUnsupportedOperation', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._2_TypeHandlerTest.testCreateDateUnsupportedOperation', absolute_path=''} ----INVOKE---> null
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest', absolute_path=''} ----CONTAIN---> Field{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest.typeHandler', absolute_path=''}
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest.setUp', absolute_path=''}
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._2_AlreadySelectedExceptionTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._2_AlreadySelectedExceptionTest.testGetOptionGroup_whenGroupIsUninitialized_shouldReturnNull', absolute_path=''}
Error processing file: /Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_1_TypeHandlerTest.java - Cannot invoke "java.lang.Integer.intValue()" because the return value of "java.util.Map.get(Object)" is null
Relationship created: TestClazz{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest.testCreateFilesWithEmptyString', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest.testCreateFilesWithEmptyString', absolute_path=''} ----INVOKE---> null
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest.testCreateDateWithEmptyString', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest.testCreateDateWithEmptyString', absolute_path=''} ----INVOKE---> null
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._2_AlreadySelectedExceptionTest.testGetOptionGroup_whenGroupIsUninitialized_shouldReturnNull', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.AlreadySelectedException.AlreadySelectedException', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest.setUp', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.OptionBuilder.withLongOpt', absolute_path=''}
Relationship already exists: Field{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest.typeHandler', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli.TypeHandler', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._2_TypeHandlerTest.testCreateDateUnsupportedOperation', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.TypeHandler.createDate', absolute_path=''}
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.testCreateThrowsIllegalArgumentExceptionWhenLongOptionIsNull', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.testCreateThrowsIllegalArgumentExceptionWhenLongOptionIsNull', absolute_path=''} ----INVOKE---> null
Target node does not exist: TestClazz{full_qualified_name='org.apache.commons.cli._2_TypeHandlerTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._2_TypeHandlerTest._2_TypeHandlerTest', absolute_path=''}
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest.setUp', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest.setUp', absolute_path=''} ----INVOKE---> null
Target node does not exist: Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.testCreateThrowsIllegalArgumentExceptionWhenLongOptionIsNull', absolute_path=''} ----INVOKE---> Field{full_qualified_name='java.lang.IllegalArgumentException.class', absolute_path=''}
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest.testCreateWithValidLongOption', absolute_path=''}
Source node does not exist: Method{full_qualified_name='org.apache.commons.cli._2_TypeHandlerTest._2_TypeHandlerTest', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli._2_TypeHandlerTest', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._2_TypeHandlerTest._2_TypeHandlerTest', absolute_path=''} ----INVOKE---> null
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._2_AlreadySelectedExceptionTest.testGetOptionGroup_whenGroupIsUninitialized_shouldReturnNull', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.AlreadySelectedException.getOptionGroup', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._2_AlreadySelectedExceptionTest.testGetOptionGroup_whenGroupIsUninitialized_shouldReturnNull', absolute_path=''} ----INVOKE---> null
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest.testCreateDateWithEmptyString', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.TypeHandler.createDate', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest.testCreateDateWithEmptyString', absolute_path=''} ----INVOKE---> null
Target node does not exist: TestClazz{full_qualified_name='org.apache.commons.cli._2_AlreadySelectedExceptionTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._2_AlreadySelectedExceptionTest._2_AlreadySelectedExceptionTest', absolute_path=''}
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest', absolute_path=''} ----CONTAIN---> Field{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.exception', absolute_path=''}
Target node does not exist: TestClazz{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest._4_TypeHandlerTest', absolute_path=''}
Source node does not exist: Method{full_qualified_name='org.apache.commons.cli._2_AlreadySelectedExceptionTest._2_AlreadySelectedExceptionTest', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli._2_AlreadySelectedExceptionTest', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._2_AlreadySelectedExceptionTest._2_AlreadySelectedExceptionTest', absolute_path=''} ----INVOKE---> null
Source node does not exist: Method{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest._4_TypeHandlerTest', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest._4_TypeHandlerTest', absolute_path=''} ----INVOKE---> null
Relationship created: Method{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest.testCreateFilesWithEmptyString', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.TypeHandler.createFiles', absolute_path=''}
Relationship already exists: Field{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.exception', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli.AlreadySelectedException', absolute_path=''}
Target node does not exist: TestClazz{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest._4_TypeHandlerTest', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest.testCreateWithValidLongOption', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.OptionBuilder.create', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest.testCreateWithValidLongOption', absolute_path=''} ----INVOKE---> null
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.testCreateThrowsIllegalArgumentExceptionWhenLongOptionIsNull', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.OptionBuilder.create', absolute_path=''}
Source node does not exist: Method{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest._4_TypeHandlerTest', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest._4_TypeHandlerTest', absolute_path=''} ----INVOKE---> null
Target node does not exist: TestClazz{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest._1_OptionBuilderTest', absolute_path=''}
Task failed: File: /Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_1_TypeHandlerTest.java caused an error.Cannot invoke "java.lang.Integer.intValue()" because the return value of "java.util.Map.get(Object)" is null

Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest.testCreateDate_validDateString', absolute_path=''}
Target node does not exist: TestClazz{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest._2_OptionBuilderTest', absolute_path=''}
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> null
Source node does not exist: Method{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest._1_OptionBuilderTest', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest._1_OptionBuilderTest', absolute_path=''} ----INVOKE---> null
Source node does not exist: Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest._2_OptionBuilderTest', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest._2_OptionBuilderTest', absolute_path=''} ----INVOKE---> null
Target node does not exist: Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli.OptionGroup.class', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest.testCreateDate_validDateString', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.TypeHandler.createDate', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest.testCreateDate_validDateString', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest.typeHandler', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest.testCreateDate_validDateString', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest.testCreateDate_validDateString', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest.testCreateDate_validDateString', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest.testCreateDate_validDateString', absolute_path=''} ----INVOKE---> null
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.exception', absolute_path=''}
Target node does not exist: Method{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest.testCreateDate_validDateString', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.junit.Assert', absolute_path=''}
Target node does not exist: TestClazz{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest._3_TypeHandlerTest', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.AlreadySelectedException.AlreadySelectedException', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> null
Source node does not exist: Method{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest._3_TypeHandlerTest', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest._3_TypeHandlerTest', absolute_path=''} ----INVOKE---> null
Target node does not exist: Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli.AlreadySelectedException.class', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> null
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.exception', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> null
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.testGetOptionGroup_withExternallyManipulatedGroup', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.testGetOptionGroup_withExternallyManipulatedGroup', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.AlreadySelectedException.getOptionGroup', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.testGetOptionGroup_withExternallyManipulatedGroup', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.exception', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.testGetOptionGroup_withExternallyManipulatedGroup', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.testGetOptionGroup_withExternallyManipulatedGroup', absolute_path=''} ----INVOKE---> null
Target node does not exist: TestClazz{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest._1_AlreadySelectedExceptionTest', absolute_path=''}
Source node does not exist: Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest._1_AlreadySelectedExceptionTest', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest._1_AlreadySelectedExceptionTest', absolute_path=''} ----INVOKE---> null
All edges have been created in the database.
All edges have been created.
================================ Updating Nodes ==================================
All test nodes have been updated.
================================== Graph Summary ==================================
Nodes created: 1 Success, 
23 Fail
Edges created:
	2 SUCCESS,
	29 RELATIONSHIP_EXIST,
	48 NODE_NOT_EXIST,
recognized 86 edges
Nodes updated:
	0 UPDATED,
	9 SKIPPED,
Graph construction completed.
Total time cost: 0h 0m 1s
