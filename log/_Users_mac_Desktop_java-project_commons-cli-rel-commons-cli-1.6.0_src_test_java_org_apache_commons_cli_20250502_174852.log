Using the following parameters:
Project Path: /Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli
Neo4j URL: bolt://localhost:7687
Username: neo4j
Password: 123456
Include test files: true
Continual Build: true
=============================== Graph Construction ===============================
=========================== Starting graph construction ===========================
Find 10 Java files.
Continual build is enabled. Keep existing nodes and relationships.
================================ Building Nodes ==================================
Building nodes in file: _4_TypeHandlerTest.java
Building nodes in file: _2_OptionBuilderTest.java
Building nodes in file: _1_TypeHandlerTest.java
Building nodes in file: _2_AlreadySelectedExceptionTest.java
Building nodes in file: _3_TypeHandlerTest.java
Building nodes in file: _1_OptionBuilderTest.java
Building nodes in file: _2_TypeHandlerTest.java
Building nodes in file: _1_AlreadySelectedExceptionTest.java
Node already exists: Method{full_qualified_name='org.apache.commons.cli._2_TypeHandlerTest.testCreateFilesWithNullInput', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_2_TypeHandlerTest.java'}
Node already exists: Method{full_qualified_name='org.apache.commons.cli._2_AlreadySelectedExceptionTest.testGetOptionGroup_whenGroupIsUninitialized_shouldReturnNull', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_2_AlreadySelectedExceptionTest.java'}
Node already exists: Method{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest.testCreateFilesWithEmptyString', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_4_TypeHandlerTest.java'}
Node already exists: Method{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest.testCreateWithValidLongOption', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_1_OptionBuilderTest.java'}
Node already exists: Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.setUp', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_2_OptionBuilderTest.java'}
Node already exists: Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_1_AlreadySelectedExceptionTest.java'}
Node already exists: Method{full_qualified_name='org.apache.commons.cli._1_TypeHandlerTest.testCreateFilesWithSpecialCharacters', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_1_TypeHandlerTest.java'}
Node already exists: Method{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest.testCreateFilesThrowsUnsupportedOperationException', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_3_TypeHandlerTest.java'}
Node already exists: Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.testCreateWithEmptyLongOption', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_2_OptionBuilderTest.java'}
Node already exists: Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.testGetOptionGroup_withExternallyManipulatedGroup', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_1_AlreadySelectedExceptionTest.java'}
Node already exists: TestClazz{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_3_TypeHandlerTest.java'}
Node already exists: TestClazz{full_qualified_name='org.apache.commons.cli._1_TypeHandlerTest', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_1_TypeHandlerTest.java'}
Node already exists: TestClazz{full_qualified_name='org.apache.commons.cli._2_AlreadySelectedExceptionTest', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_2_AlreadySelectedExceptionTest.java'}
Node already exists: TestClazz{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_4_TypeHandlerTest.java'}
Node already exists: TestClazz{full_qualified_name='org.apache.commons.cli._2_TypeHandlerTest', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_2_TypeHandlerTest.java'}
Node already exists: TestClazz{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_1_OptionBuilderTest.java'}
Building nodes in file: _3_OptionBuilderTest.java
Building nodes in file: _3_OptionBuilderTest.java
Node already exists: Field{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.exception', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_1_AlreadySelectedExceptionTest.java'}
Node already exists: TestClazz{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_2_OptionBuilderTest.java'}
Node already exists: TestClazz{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_1_AlreadySelectedExceptionTest.java'}
Node already exists: Method{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest.testCreate_throwsExceptionWhenLongOptionIsNotSet', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_3_OptionBuilderTest.java'}
Node already exists: TestClazz{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/org/apache/commons/cli/_3_OptionBuilderTest.java'}
Node created: Method{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest.testCreateWithInvalidCharacter', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/_3_OptionBuilderTest.java'}
Node already exists: TestClazz{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest', absolute_path='/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0/src/test/java/org/apache/commons/cli/_3_OptionBuilderTest.java'}
All nodes have been created in the database.
All nodes have been created.
================================ Reading Nodes ==================================
Update all nodes.
================================ Building Edges ==================================
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._2_TypeHandlerTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._2_TypeHandlerTest.testCreateFilesWithNullInput', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._2_TypeHandlerTest.testCreateFilesWithNullInput', absolute_path=''} ----INVOKE---> null
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._2_AlreadySelectedExceptionTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._2_AlreadySelectedExceptionTest.testGetOptionGroup_whenGroupIsUninitialized_shouldReturnNull', absolute_path=''}
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest.testCreateWithValidLongOption', absolute_path=''}
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest.testCreateFilesWithEmptyString', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest.testCreateFilesWithEmptyString', absolute_path=''} ----INVOKE---> null
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._2_AlreadySelectedExceptionTest.testGetOptionGroup_whenGroupIsUninitialized_shouldReturnNull', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.AlreadySelectedException.AlreadySelectedException', absolute_path=''}
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.setUp', absolute_path=''}
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest.testCreateFilesThrowsUnsupportedOperationException', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest.testCreateFilesThrowsUnsupportedOperationException', absolute_path=''} ----INVOKE---> null
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._2_AlreadySelectedExceptionTest.testGetOptionGroup_whenGroupIsUninitialized_shouldReturnNull', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.AlreadySelectedException.getOptionGroup', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._2_AlreadySelectedExceptionTest.testGetOptionGroup_whenGroupIsUninitialized_shouldReturnNull', absolute_path=''} ----INVOKE---> null
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest.testCreateFilesWithEmptyString', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.TypeHandler.createFiles', absolute_path=''}
Target node does not exist: TestClazz{full_qualified_name='org.apache.commons.cli._2_AlreadySelectedExceptionTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._2_AlreadySelectedExceptionTest._2_AlreadySelectedExceptionTest', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest.testCreateFilesThrowsUnsupportedOperationException', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.TypeHandler.createFiles', absolute_path=''}
Target node does not exist: TestClazz{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest._4_TypeHandlerTest', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest.testCreateWithValidLongOption', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.OptionBuilder.withLongOpt', absolute_path=''}
Source node does not exist: Method{full_qualified_name='org.apache.commons.cli._2_AlreadySelectedExceptionTest._2_AlreadySelectedExceptionTest', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli._2_AlreadySelectedExceptionTest', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._2_AlreadySelectedExceptionTest._2_AlreadySelectedExceptionTest', absolute_path=''} ----INVOKE---> null
Target node does not exist: TestClazz{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest._3_TypeHandlerTest', absolute_path=''}
Source node does not exist: Method{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest._4_TypeHandlerTest', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._2_TypeHandlerTest.testCreateFilesWithNullInput', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.TypeHandler.createFiles', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._4_TypeHandlerTest._4_TypeHandlerTest', absolute_path=''} ----INVOKE---> null
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest', absolute_path=''} ----CONTAIN---> Field{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.exception', absolute_path=''}
Source node does not exist: Method{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest._3_TypeHandlerTest', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._3_TypeHandlerTest._3_TypeHandlerTest', absolute_path=''} ----INVOKE---> null
Target node does not exist: TestClazz{full_qualified_name='org.apache.commons.cli._2_TypeHandlerTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._2_TypeHandlerTest._2_TypeHandlerTest', absolute_path=''}
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._1_TypeHandlerTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._1_TypeHandlerTest.testCreateFilesWithSpecialCharacters', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._1_TypeHandlerTest.testCreateFilesWithSpecialCharacters', absolute_path=''} ----INVOKE---> null
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest.testCreateWithValidLongOption', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.OptionBuilder.create', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest.testCreateWithValidLongOption', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest.testCreateWithValidLongOption', absolute_path=''} ----INVOKE---> null
Source node does not exist: Method{full_qualified_name='org.apache.commons.cli._2_TypeHandlerTest._2_TypeHandlerTest', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli._2_TypeHandlerTest', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._2_TypeHandlerTest._2_TypeHandlerTest', absolute_path=''} ----INVOKE---> null
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.setUp', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.OptionBuilder.withLongOpt', absolute_path=''}
Relationship already exists: Field{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.exception', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli.AlreadySelectedException', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._1_TypeHandlerTest.testCreateFilesWithSpecialCharacters', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.TypeHandler.createFiles', absolute_path=''}
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest.testCreate_throwsExceptionWhenLongOptionIsNotSet', absolute_path=''}
Target node does not exist: TestClazz{full_qualified_name='org.apache.commons.cli._1_TypeHandlerTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._1_TypeHandlerTest._1_TypeHandlerTest', absolute_path=''}
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.testCreateWithEmptyLongOption', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest.testCreateWithValidLongOption', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.Option.getLongOpt', absolute_path=''}
Source node does not exist: Method{full_qualified_name='org.apache.commons.cli._1_TypeHandlerTest._1_TypeHandlerTest', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli._1_TypeHandlerTest', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest.testCreate_throwsExceptionWhenLongOptionIsNotSet', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.OptionBuilder.create', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._1_TypeHandlerTest._1_TypeHandlerTest', absolute_path=''} ----INVOKE---> null
Target node does not exist: TestClazz{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest._1_OptionBuilderTest', absolute_path=''}
Target node does not exist: TestClazz{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest._3_OptionBuilderTest', absolute_path=''}
Relationship created: TestClazz{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest.testCreateWithInvalidCharacter', absolute_path=''}
Source node does not exist: Method{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest._1_OptionBuilderTest', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._1_OptionBuilderTest._1_OptionBuilderTest', absolute_path=''} ----INVOKE---> null
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> null
Source node does not exist: Method{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest._3_OptionBuilderTest', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest._3_OptionBuilderTest', absolute_path=''} ----INVOKE---> null
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.testCreateWithEmptyLongOption', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.OptionBuilder.withLongOpt', absolute_path=''}
Target node does not exist: Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli.OptionGroup.class', absolute_path=''}
Relationship created: Method{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest.testCreateWithInvalidCharacter', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.OptionBuilder.create', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.testCreateWithEmptyLongOption', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.OptionBuilder.create', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.testCreateWithEmptyLongOption', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.testCreateWithEmptyLongOption', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.testCreateWithEmptyLongOption', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.testCreateWithEmptyLongOption', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest.testCreateWithEmptyLongOption', absolute_path=''} ----INVOKE---> null
Target node does not exist: TestClazz{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest._3_OptionBuilderTest', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.exception', absolute_path=''}
Target node does not exist: TestClazz{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest._2_OptionBuilderTest', absolute_path=''}
Source node does not exist: Method{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest._3_OptionBuilderTest', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._3_OptionBuilderTest._3_OptionBuilderTest', absolute_path=''} ----INVOKE---> null
Source node does not exist: Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest._2_OptionBuilderTest', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._2_OptionBuilderTest._2_OptionBuilderTest', absolute_path=''} ----INVOKE---> null
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.AlreadySelectedException.AlreadySelectedException', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> null
Target node does not exist: Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli.AlreadySelectedException.class', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> null
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.exception', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.setUp', absolute_path=''} ----INVOKE---> null
Relationship already exists: TestClazz{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.testGetOptionGroup_withExternallyManipulatedGroup', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.testGetOptionGroup_withExternallyManipulatedGroup', absolute_path=''} ----INVOKE---> Method{full_qualified_name='org.apache.commons.cli.AlreadySelectedException.getOptionGroup', absolute_path=''}
Relationship already exists: Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.testGetOptionGroup_withExternallyManipulatedGroup', absolute_path=''} ----INVOKE---> Field{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.exception', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.testGetOptionGroup_withExternallyManipulatedGroup', absolute_path=''} ----INVOKE---> null
Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest.testGetOptionGroup_withExternallyManipulatedGroup', absolute_path=''} ----INVOKE---> null
Target node does not exist: TestClazz{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest', absolute_path=''} ----CONTAIN---> Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest._1_AlreadySelectedExceptionTest', absolute_path=''}
Source node does not exist: Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest._1_AlreadySelectedExceptionTest', absolute_path=''} ----DEPENDENCY---> Clazz{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest', absolute_path=''}
Method{full_qualified_name='org.apache.commons.cli._1_AlreadySelectedExceptionTest._1_AlreadySelectedExceptionTest', absolute_path=''} ----INVOKE---> null
All edges have been created in the database.
All edges have been created.
================================ Updating Nodes ==================================
All test nodes have been updated.
================================== Graph Summary ==================================
Nodes created: 1 Success, 
22 Fail
Edges created:
	2 SUCCESS,
	31 RELATIONSHIP_EXIST,
	52 NODE_NOT_EXIST,
recognized 85 edges
Nodes updated:
	0 UPDATED,
	9 SKIPPED,
Graph construction completed.
Total time cost: 0h 0m 0s
