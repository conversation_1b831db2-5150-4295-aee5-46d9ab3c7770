# TestAgent v2.0 核心依赖
# 这是新版本的最小依赖列表，包含了实时可视化和任务管理功能所需的包

# Web框架和API
fastapi>=0.100.0
uvicorn[standard]>=0.20.0
websockets>=11.0.0
pydantic>=2.0.0
pydantic-settings>=2.0.0

# HTTP客户端和工具
requests>=2.28.0
httpx>=0.24.0

# 异步支持
asyncio-mqtt>=0.11.0
aiofiles>=23.0.0

# 数据处理
python-multipart>=0.0.6
orjson>=3.8.0

# 原有系统依赖（保持兼容）
langchain>=0.1.0
langchain-community>=0.0.20
langchain-core>=0.1.0
langchain-openai>=0.0.8
langgraph>=0.0.40
neo4j>=5.0.0
openai>=1.0.0

# 日志和监控
structlog>=23.0.0
rich>=13.0.0

# 开发和测试工具（可选）
pytest>=7.0.0
pytest-asyncio>=0.21.0
httpx>=0.24.0

# 系统工具
psutil>=5.9.0
python-dotenv>=1.0.0

# 数据科学和分析（如果需要）
pandas>=2.0.0
numpy>=1.24.0

# 安全
cryptography>=40.0.0

# 其他工具
click>=8.0.0
typer>=0.9.0
tqdm>=4.65.0
