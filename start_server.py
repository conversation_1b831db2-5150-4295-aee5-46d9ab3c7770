#!/usr/bin/env python3
"""
TestAgent v2.0 服务启动脚本
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path

def check_dependencies():
    """检查必要的依赖是否已安装"""
    required_packages = [
        'fastapi',
        'uvicorn',
        'websockets',
        'pydantic',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_neo4j_connection(url, username, password):
    """检查Neo4j连接"""
    try:
        from neo4j import GraphDatabase
        driver = GraphDatabase.driver(url, auth=(username, password))
        with driver.session() as session:
            session.run("RETURN 1")
        driver.close()
        print("✅ Neo4j连接正常")
        return True
    except Exception as e:
        print(f"❌ Neo4j连接失败: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    directories = [
        "result/stream",
        "result/report",
        "log"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ 目录结构创建完成")

def start_server(host="0.0.0.0", port=8000, reload=False):
    """启动FastAPI服务器"""
    try:
        import uvicorn
        print(f"🚀 启动TestAgent服务器...")
        print(f"   地址: http://{host}:{port}")
        print(f"   WebSocket: ws://{host}:{port}/ws/{{task_id}}")
        print(f"   API文档: http://{host}:{port}/docs")
        print(f"   演示页面: 请打开 demo.html")
        print("\n按 Ctrl+C 停止服务器")
        
        uvicorn.run(
            "main:app",
            host=host,
            port=port,
            reload=reload,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")

def main():
    parser = argparse.ArgumentParser(description="TestAgent v2.0 服务启动器")
    parser.add_argument("--host", default="0.0.0.0", help="服务器地址 (默认: 0.0.0.0)")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口 (默认: 8000)")
    parser.add_argument("--reload", action="store_true", help="启用自动重载 (开发模式)")
    parser.add_argument("--check-neo4j", action="store_true", help="检查Neo4j连接")
    parser.add_argument("--neo4j-url", default="bolt://localhost:7687", help="Neo4j URL")
    parser.add_argument("--neo4j-user", default="neo4j", help="Neo4j用户名")
    parser.add_argument("--neo4j-password", default="123456", help="Neo4j密码")
    
    args = parser.parse_args()
    
    print("🧪 TestAgent v2.0 - 智能测试用例生成系统")
    print("=" * 50)
    
    # 检查依赖
    print("🔍 检查依赖...")
    if not check_dependencies():
        sys.exit(1)
    
    # 创建目录
    print("📁 创建目录...")
    create_directories()
    
    # 检查Neo4j连接（可选）
    if args.check_neo4j:
        print("🔗 检查Neo4j连接...")
        if not check_neo4j_connection(args.neo4j_url, args.neo4j_user, args.neo4j_password):
            print("⚠️  Neo4j连接失败，但服务器仍会启动")
    
    # 启动服务器
    start_server(args.host, args.port, args.reload)

if __name__ == "__main__":
    main()
