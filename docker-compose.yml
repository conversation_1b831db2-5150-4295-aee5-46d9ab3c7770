version: '3.8'

services:
  # TestAgent主服务
  testagent:
    build: .
    container_name: testagent-api
    ports:
      - "8000:8000"
    environment:
      - TESTAGENT_ENV=production
      - TESTAGENT_NEO4J_URL=bolt://neo4j:7687
      - TESTAGENT_NEO4J_USERNAME=neo4j
      - TESTAGENT_NEO4J_PASSWORD=testagent123
      - TESTAGENT_MAX_CONCURRENT_TASKS=10
      - TESTAGENT_LOG_LEVEL=INFO
    volumes:
      - ./log:/app/log
      - ./result:/app/result
    depends_on:
      - neo4j
    networks:
      - testagent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Neo4j数据库
  neo4j:
    image: neo4j:5.15-community
    container_name: testagent-neo4j
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    environment:
      - NEO4J_AUTH=neo4j/testagent123
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      - NEO4J_dbms_memory_heap_initial__size=512m
      - NEO4J_dbms_memory_heap_max__size=2G
      - NEO4J_dbms_memory_pagecache_size=1G
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
    networks:
      - testagent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "testagent123", "RETURN 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Redis缓存（可选，用于任务队列和缓存）
  redis:
    image: redis:7-alpine
    container_name: testagent-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass testagent123
    volumes:
      - redis_data:/data
    networks:
      - testagent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: testagent-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - testagent
    networks:
      - testagent-network
    restart: unless-stopped

  # 监控服务（可选）
  prometheus:
    image: prom/prometheus:latest
    container_name: testagent-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - testagent-network
    restart: unless-stopped

  # Grafana仪表板（可选）
  grafana:
    image: grafana/grafana:latest
    container_name: testagent-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=testagent123
    volumes:
      - grafana_data:/var/lib/grafana
    depends_on:
      - prometheus
    networks:
      - testagent-network
    restart: unless-stopped

volumes:
  neo4j_data:
  neo4j_logs:
  neo4j_import:
  neo4j_plugins:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  testagent-network:
    driver: bridge
