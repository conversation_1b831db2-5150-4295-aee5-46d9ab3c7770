#!/usr/bin/env python3
"""
测试 /generate_test_case_stream_java 端点
"""

import requests
import sseclient
import json

def test_java_streaming_api():
    # 测试 Java 专用端点
    url = 'http://localhost:8000/generate_test_case_stream_java'

    # 请求体参数
    payload = {
        "method_id": 44777,
        "url": "bolt://localhost:7687",
        "username": "neo4j",
        "password": "123456",
        "limit": 50
    }

    print(f"🧪 测试 Java 流式端点: {url}")
    print(f"📦 请求参数: {json.dumps(payload, indent=2)}")

    # 保存结构化报告的列表
    all_reports = []

    try:
        # 发起 POST 请求，并读取 SSE 流
        with requests.post(url, json=payload, stream=True) as response:
            print(f"📊 响应状态码: {response.status_code}")
            
            if response.status_code != 200:
                print(f"❌ 请求失败: {response.text}")
                return []
            
            client = sseclient.SSEClient(response)
            print("✅ Connected to Java stream. Output:\n")

            for event in client.events():
                line = event.data.strip()

                # 打印所有内容（用于调试可视化）
                print(line)

                # 捕获结构化 JSON 报告
                if line.startswith("##REPORT##:"):
                    try:
                        json_obj = json.loads(line[len("##REPORT##:"):])
                        all_reports.append(json_obj)
                    except Exception as e:
                        print(f"[!] Failed to parse JSON: {e}")

                # 结束条件
                if "[Done]" in line:
                    print("\n🎉 Java test generation completed.\n")
                    break

    except Exception as e:
        print(f"❌ 请求异常: {e}")

    # 返回结构化结果
    return all_reports

if __name__ == "__main__":
    print("🚀 测试 Java 测试用例生成端点...")
    print("=" * 50)
    
    ret = test_java_streaming_api()
    
    print("=" * 50)
    print("📊 Structure Report:")
    print(json.dumps(ret, indent=2, ensure_ascii=False))
    
    if ret:
        print(f"✅ 成功生成了 {len(ret)} 个测试报告")
    else:
        print("ℹ️  没有生成测试报告（可能由于配额限制或其他业务逻辑问题）")
