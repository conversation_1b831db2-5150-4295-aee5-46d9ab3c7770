import json
from Node import *
import os
from CKGRetriever import CKGRetriever
from EnvironmentService import EnvironmentService
import re

def read_jsonl(file_path):
    data = []
    with open(file_path, 'r') as f:
        for line in f:
            data.append(json.loads(line))
    return data

project_path = "/Users/<USER>/Desktop/TestAgents/TestAgent_wo_function_call/result/csv-0402-gpt_4o(woTool)"
record_path = os.path.join(project_path, "record.jsonl")
test_case_path = os.path.join(project_path, "test_case.jsonl")

records = read_jsonl(record_path)
test_cases = read_jsonl(test_case_path)

graph_retriever = CKGRetriever("bolt://localhost:7687", "neo4j", "123456")
envServer = EnvironmentService()
# envServer.set_base_path("/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0")
envServer.set_base_path("/Users/<USER>/Desktop/java-project/commons-csv-rel-commons-csv-1.10.0")
# envServer.set_base_path("/Users/<USER>/Desktop/java-project/gson-gson-parent-2.12.0/gson")
# envServer.set_base_path("/Users/<USER>/Desktop/java-project/event-ruler-1.8.0")
# envServer.set_base_path("/Users/<USER>/Desktop/java-project/commons-lang-rel-commons-lang-3.10")
# envServer.set_base_path("/Users/<USER>/Desktop/java-project/jfreechart-1.5.5")

for record in records:
    name = record['name']
    test_case_item = None
    for item in test_cases:
        if item['name'] == name:
            test_case_item = item
            break

    assert isinstance(test_case_item, dict)

    method_signature = test_case_item['signature']
    focal_method = graph_retriever.search_method_by_signature(method_signature)
    inject_path = focal_method.absolute_path.replace("src/main", "src/test")
    inject_dir = inject_path[:inject_path.rfind("/")]
    envServer.set_inject_dir(inject_dir)
    for index in range(len(record['ret'])):
        # if index < 3:
        #     continue
        test_case = test_case_item['test_case'][index]
        class_pattern = r'public class\s+(\w+)\s*(?:\{|\s+extends|\s+implements|$)'
        matches = re.findall(class_pattern, test_case)
        test_class_name = matches[0]
        add_test_class_name = "_" + str(envServer.number) + "_" + test_class_name
        envServer.number += 1
        test_case = test_case.replace(test_class_name, add_test_class_name)
        ret = envServer.add_test_to_CKG(test_case, add_test_class_name)
        if ret["result"] == "Error":
            raise Exception("Failed to add test case to CKG.")
        focal_clazz_name = focal_method.class_name
        focal_method_fq_name = focal_method.full_qualified_name
        indexxx = focal_method_fq_name.find(focal_clazz_name)
        if indexxx != -1:
            focal_clazz_fq_name = focal_method_fq_name[:indexxx + len(focal_clazz_name)]
        else:
            focal_clazz_fq_name = focal_clazz_name
        find_bug = record['find_bugs'][index]
        requirement = test_case_item["test_point"][index]
        test_report = record['ret'][index]  # "Success" or "Execute Error" or "Compile Error" or "Syntax Error"
        coverage_rate = record['line_coverage_rates'][index]
        coverage_lines = record['covered_lines']
        mutation_score = record['mutation_scores'][index]
        mutants = test_case_item['mutants_list'][index] if index < len(test_case_item['mutants_list']) else {}
        graph_retriever.update_test_class(add_test_class_name, focal_clazz_fq_name, focal_method_fq_name, method_signature,
                                      test_report, coverage_rate, coverage_lines, mutation_score, json.dumps(mutants),
                                      find_bug, json.dumps(requirement))

        print(f"Test case {index} added to CKG successfully.")