import json
from EnvironmentService import EnvironmentService
from focal_method_extract import load_methods_from_jsonl
import re


def read_jsonl(file_path):
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            if line.strip():  # 忽略空行
                data.append(json.loads(line))
    return data


def write_jsonl(data, path):
    with open(path, 'a', encoding='utf-8') as f:
        for item in data:
            json_line = json.dumps(item, ensure_ascii=False)
            f.write(json_line + '\n')


envServer = EnvironmentService()
envServer.set_base_path("/Users/<USER>/Desktop/java-project/commons-cli-rel-commons-cli-1.6.0")
# envServer.set_base_path("/Users/<USER>/Desktop/java-project/commons-csv-rel-commons-csv-1.10.0")
# envServer.set_base_path("/Users/<USER>/Desktop/java-project/gson-gson-parent-2.12.0/gson")
# envServer.set_base_path("/Users/<USER>/Desktop/java-project/windward-1.5.3-RELEASE")
# envServer.set_base_path("/Users/<USER>/Desktop/java-project/event-ruler-1.8.0")
# envServer.set_base_path("/Users/<USER>/Desktop/java-project/commons-lang-rel-commons-lang-3.10")
# envServer.set_base_path("/Users/<USER>/Desktop/java-project/jfreechart-1.5.5")


def run_test_for_bc(result_path, meta_path, skip_number=0, reference_path=None):
    methods_list = load_methods_from_jsonl(meta_path)
    result_list = read_jsonl(result_path + "/record.jsonl")
    test_list = read_jsonl(result_path + "/test_case.jsonl")
    reference_list = None
    reference_test_list = None
    if reference_path is not None:
        reference_list = read_jsonl(reference_path + "/record.jsonl")
        reference_test_list = read_jsonl(reference_path + "/test_case.jsonl")

    index = 0

    for method in methods_list:
        if index < skip_number:
            index += 1
            continue
        envServer.total_test_remove()
        inject_path = method.absolute_path.replace("src/main", "src/test")
        inject_dir = inject_path[:inject_path.rfind("/")]
        envServer.set_inject_dir(inject_dir)
        result = result_list[index]
        name = result["name"]
        testcase = None
        for test in test_list:
            if test["name"] == name:
                testcase = test
                break
        reference_result = None
        reference_testcase = None
        if reference_path is not None:
            assert reference_test_list is not None
            assert reference_list is not None
            reference_result = reference_list[index]
            reference_testcase = None
            for test in reference_test_list:
                if test["name"] == name:
                    reference_testcase = test
                    break

        correct_test_list = []
        if testcase is not None:
            ret = result["ret"]
            test = testcase["test_case"]
            if len(ret) == len(test) + 1:
                ret = ret[:len(ret) - 1]
            assert len(ret) == len(test)
            for t in range(len(ret)):
                if ret[t] == "Success" or ret[t] == "Execute Error":
                    correct_test_list.append(test[t])
        if reference_path is not None:
            assert reference_result is not None
            assert reference_testcase is not None
            reference_ret = reference_result["ret"]
            reference_test = reference_testcase["test_case"]
            for t in range(len(reference_ret)):
                if (reference_ret[t] == "Success" or reference_ret[t] == "Execute Error") and t < len(reference_test):
                    correct_test_list.append(reference_test[t])

        if len(correct_test_list) == 0:
            index += 1
            save_data = {"name": name, "coverage": None}
            write_jsonl([save_data], result_path + "/coverage.jsonl")
            continue

        under_test_list = []
        for i, each_test in enumerate(correct_test_list):
            class_pattern = r'public class\s+(\w+)\s*(?:\{|\s+extends|\s+implements|$)'
            matches = re.findall(class_pattern, each_test)
            test_class_name = matches[0]
            new_test_name = "A" + str(i) + test_class_name
            test_case = each_test.replace(test_class_name, new_test_name)
            under_test_list.append({"test": test_case, "name": new_test_name})
            envServer.set_test_infor(new_test_name, test_case)
            envServer.test_code_inject()

        package_name = method.package_name
        class_name = method.full_qualified_name.rsplit('.', 1)[0]
        start_line = method.start_line
        end_line = method.end_line
        signature = method.signature
        run_coverage_ret = envServer.simple_run_coverage_test(package_name, class_name, signature, start_line, end_line)
        save_data = {"name": name, "coverage": run_coverage_ret}
        write_jsonl([save_data], result_path + "/coverage.jsonl")
        index += 1

        print("Finish: ", index)

def run_test_for_mutation(result_path, meta_path, skip_number=0, reference_path=None):
    methods_list = load_methods_from_jsonl(meta_path)
    result_list = read_jsonl(result_path + "/record.jsonl")
    test_list = read_jsonl(result_path + "/test_case.jsonl")
    reference_list = None
    reference_test_list = None
    if reference_path is not None:
        reference_list = read_jsonl(reference_path + "/record.jsonl")
        reference_test_list = read_jsonl(reference_path + "/test_case.jsonl")

    index = 0

    for method in methods_list:
        if index < skip_number:
            index += 1
            continue
        envServer.total_test_remove()
        inject_path = method.absolute_path.replace("src/main", "src/test")
        inject_dir = inject_path[:inject_path.rfind("/")]
        envServer.set_inject_dir(inject_dir)
        result = result_list[index]
        name = result["name"]
        testcase = None
        for test in test_list:
            if test["name"] == name:
                testcase = test
                break
        reference_result = None
        reference_testcase = None
        if reference_path is not None:
            assert reference_test_list is not None
            assert reference_list is not None
            reference_result = reference_list[index]
            reference_testcase = None
            for test in reference_test_list:
                if test["name"] == name:
                    reference_testcase = test
                    break

        correct_test_list = []
        if testcase is not None:
            ret = result["ret"]
            test = testcase["test_case"]
            if len(ret) == len(test) + 1:
                ret = ret[:len(ret) - 1]
            assert len(ret) == len(test)
            for t in range(len(ret)):
                if ret[t] == "Success":
                    correct_test_list.append(test[t])
        if reference_path is not None:
            assert reference_result is not None
            assert reference_testcase is not None
            reference_ret = reference_result["ret"]
            reference_test = reference_testcase["test_case"]
            for t in range(len(reference_ret)):
                if reference_ret[t] == "Success":
                    correct_test_list.append(reference_test[t])

        if len(correct_test_list) == 0:
            index += 1
            save_data = {"name": name, "mutation": None}
            write_jsonl([save_data], result_path + "/mutation1.jsonl")
            continue

        under_test_list = []
        for i, each_test in enumerate(correct_test_list):
            class_pattern = r'public class\s+(\w+)\s*(?:\{|\s+extends|\s+implements|$)'
            matches = re.findall(class_pattern, each_test)
            test_class_name = matches[0]
            new_test_name = "A" + str(i) + test_class_name
            test_case = each_test.replace(test_class_name, new_test_name)
            under_test_list.append({"test": test_case, "name": new_test_name})
            envServer.set_test_infor(new_test_name, test_case)
            envServer.test_code_inject()

        package_name = method.package_name
        class_name = method.full_qualified_name.rsplit('.', 1)[0]
        start_line = method.start_line
        end_line = method.end_line
        run_mutation_ret = envServer.simple_run_mutation_test(package_name, class_name, start_line, end_line)
        save_data = {"name": name, "mutation": run_mutation_ret}
        write_jsonl([save_data], result_path + "/mutation1.jsonl")
        index += 1

        print("Finish: ", index)

if __name__ == '__main__':
    meta_path = "result/focal_method/cli_new.jsonl"
    result_path_1 = "/Users/<USER>/Desktop/TestAgents/TestAgent_wo_function_call/result/cli-0403-gpt_4o(woTool)"
    # run_test_for_bc(result_path_1, meta_path)
    # run_test_for_mutation(result_path_1, meta_path)
    #
    # result_path = "/Users/<USER>/Desktop/TestAgents/TestAgent_wo_function_call/result/gson-0403-gpt_4o(woTool)"
    # run_test_for_bc(result_path, meta_path)
    # run_test_for_mutation(result_path, meta_path)

    # result_path = "/Users/<USER>/Desktop/TestAgents/TestAgent_wo_function_call/result/gson-0322-gpt_4o(eval)"
    # run_test_for_bc(result_path, meta_path, 0, result_path_1)
    # run_test_for_mutation(result_path, meta_path, 0, result_path_1)

    result_path = "/Users/<USER>/Desktop/TestAgents/TestAgent_wo_function_call/result/cli-0403-gpt_4o(woTool)(eval_CC)"
    run_test_for_bc(result_path, meta_path, 0, result_path_1)
    run_test_for_mutation(result_path, meta_path, 0, result_path_1)

