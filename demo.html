<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TestAgent - 智能测试用例生成系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }
        
        .form-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #4facfe;
        }
        
        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .progress-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .status-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .status-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #4facfe;
        }
        
        .status-item h4 {
            color: #333;
            margin-bottom: 5px;
        }
        
        .status-item p {
            color: #666;
            font-size: 14px;
        }
        
        .steps-container {
            background: white;
            border-radius: 6px;
            padding: 20px;
        }
        
        .step-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .step-item:last-child {
            border-bottom: none;
        }
        
        .step-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        
        .step-icon.waiting {
            background: #e9ecef;
            color: #6c757d;
        }
        
        .step-icon.running {
            background: #ffc107;
            color: white;
            animation: pulse 1.5s infinite;
        }
        
        .step-icon.completed {
            background: #28a745;
            color: white;
        }
        
        .step-icon.failed {
            background: #dc3545;
            color: white;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .log-container {
            background: #1e1e1e;
            color: #f8f8f2;
            padding: 20px;
            border-radius: 6px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 5px;
        }
        
        .log-entry.info { color: #8be9fd; }
        .log-entry.success { color: #50fa7b; }
        .log-entry.warning { color: #ffb86c; }
        .log-entry.error { color: #ff5555; }
        
        .results-section {
            grid-column: 1 / -1;
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .test-case {
            background: white;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #28a745;
        }
        
        .test-case h4 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .test-case pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 14px;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 TestAgent</h1>
            <p>智能测试用例生成系统 - 实时可视化版本</p>
        </div>
        
        <div class="main-content">
            <div class="form-section">
                <h3>📝 创建测试生成任务</h3>
                <form id="taskForm">
                    <div class="form-group">
                        <label for="methodId">方法ID</label>
                        <input type="number" id="methodId" value="44777" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="url">数据库URL</label>
                        <input type="text" id="url" value="bolt://localhost:7687" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="username">用户名</label>
                        <input type="text" id="username" value="neo4j" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">密码</label>
                        <input type="password" id="password" value="123456" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="limit">递归限制</label>
                        <input type="number" id="limit" value="50" min="1" max="100">
                    </div>
                    
                    <button type="submit" class="btn" id="submitBtn">🚀 开始生成测试用例</button>
                </form>
            </div>
            
            <div class="progress-section">
                <h3>📊 任务进度</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                
                <div class="status-info">
                    <div class="status-item">
                        <h4>任务状态</h4>
                        <p id="taskStatus">等待开始</p>
                    </div>
                    <div class="status-item">
                        <h4>当前步骤</h4>
                        <p id="currentStep">-</p>
                    </div>
                </div>
                
                <div class="steps-container">
                    <h4>执行步骤</h4>
                    <div id="stepsContainer">
                        <!-- 步骤将通过JavaScript动态添加 -->
                    </div>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <div class="form-section">
                <h3>📋 实时日志</h3>
                <div class="log-container" id="logContainer">
                    <div class="log-entry info">等待任务开始...</div>
                </div>
            </div>
            
            <div class="form-section">
                <h3>⚙️ 任务管理</h3>
                <div class="status-info">
                    <div class="status-item">
                        <h4>任务ID</h4>
                        <p id="taskId">-</p>
                    </div>
                    <div class="status-item">
                        <h4>创建时间</h4>
                        <p id="createTime">-</p>
                    </div>
                </div>
                <button class="btn" id="cancelBtn" style="background: #dc3545; margin-top: 15px;" disabled>❌ 取消任务</button>
            </div>
        </div>
        
        <div class="results-section hidden" id="resultsSection">
            <h3>🎉 生成结果</h3>
            <div id="testCasesContainer">
                <!-- 测试用例结果将通过JavaScript动态添加 -->
            </div>
        </div>
    </div>

    <script>
        class TestAgentUI {
            constructor() {
                this.baseUrl = 'http://localhost:8000';
                this.wsUrl = 'ws://localhost:8000';
                this.currentTaskId = null;
                this.websocket = null;
                
                this.initializeEventListeners();
                this.initializeSteps();
            }
            
            initializeEventListeners() {
                document.getElementById('taskForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.createTask();
                });
                
                document.getElementById('cancelBtn').addEventListener('click', () => {
                    this.cancelTask();
                });
            }
            
            initializeSteps() {
                const steps = [
                    { name: 'initState', label: '初始化状态' },
                    { name: 'methodAnalyzer', label: '方法分析' },
                    { name: 'testPointsGenerator', label: '测试点生成' },
                    { name: 'testCaseGenerator', label: '测试用例生成' },
                    { name: 'testCaseAcceptor', label: '测试用例验收' },
                    { name: 'reportGeneration', label: '报告生成' }
                ];
                
                const container = document.getElementById('stepsContainer');
                container.innerHTML = '';
                
                steps.forEach(step => {
                    const stepElement = document.createElement('div');
                    stepElement.className = 'step-item';
                    stepElement.innerHTML = `
                        <div class="step-icon waiting" id="step-${step.name}">⏳</div>
                        <span>${step.label}</span>
                    `;
                    container.appendChild(stepElement);
                });
            }
            
            async createTask() {
                const formData = {
                    method_id: parseInt(document.getElementById('methodId').value),
                    url: document.getElementById('url').value,
                    username: document.getElementById('username').value,
                    password: document.getElementById('password').value,
                    limit: parseInt(document.getElementById('limit').value)
                };
                
                try {
                    this.setUIState('creating');
                    
                    const response = await fetch(`${this.baseUrl}/tasks`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(formData)
                    });
                    
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    
                    const result = await response.json();
                    this.currentTaskId = result.task_id;
                    
                    document.getElementById('taskId').textContent = this.currentTaskId;
                    document.getElementById('createTime').textContent = new Date().toLocaleString();
                    
                    this.addLog('success', `任务创建成功: ${this.currentTaskId}`);
                    this.connectWebSocket();
                    
                } catch (error) {
                    this.addLog('error', `创建任务失败: ${error.message}`);
                    this.setUIState('idle');
                }
            }
            
            connectWebSocket() {
                if (this.websocket) {
                    this.websocket.close();
                }
                
                const wsUrl = `${this.wsUrl}/ws/${this.currentTaskId}`;
                this.websocket = new WebSocket(wsUrl);
                
                this.websocket.onopen = () => {
                    this.addLog('info', 'WebSocket连接已建立');
                    this.setUIState('running');
                };
                
                this.websocket.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        this.handleWebSocketMessage(data);
                    } catch (error) {
                        this.addLog('error', `解析WebSocket消息失败: ${error.message}`);
                    }
                };
                
                this.websocket.onclose = () => {
                    this.addLog('warning', 'WebSocket连接已关闭');
                };
                
                this.websocket.onerror = (error) => {
                    this.addLog('error', `WebSocket错误: ${error.message}`);
                };
            }
            
            handleWebSocketMessage(data) {
                const { type, task_id, data: messageData } = data;
                
                switch (type) {
                    case 'progress':
                        this.updateProgress(messageData);
                        break;
                    case 'step_update':
                        this.updateStep(messageData);
                        break;
                    case 'log':
                        this.handleLogMessage(messageData);
                        break;
                    case 'result':
                        this.handleResult(messageData);
                        break;
                    case 'error':
                        this.handleError(messageData);
                        break;
                }
            }
            
            updateProgress(data) {
                const progress = data.progress || 0;
                const status = data.status || 'unknown';
                const message = data.message || '';
                
                document.getElementById('progressFill').style.width = `${progress}%`;
                document.getElementById('taskStatus').textContent = status;
                
                if (message) {
                    this.addLog('info', message);
                }
            }
            
            updateStep(data) {
                const stepName = data.step_name;
                const status = data.status;
                
                document.getElementById('currentStep').textContent = stepName;
                
                const stepIcon = document.getElementById(`step-${stepName}`);
                if (stepIcon) {
                    stepIcon.className = `step-icon ${status}`;
                    stepIcon.textContent = this.getStepIcon(status);
                }
                
                this.addLog('info', `步骤更新: ${stepName} -> ${status}`);
            }
            
            getStepIcon(status) {
                const icons = {
                    waiting: '⏳',
                    running: '🔄',
                    completed: '✅',
                    failed: '❌',
                    skipped: '⏭️'
                };
                return icons[status] || '❓';
            }
            
            handleLogMessage(data) {
                if (data.method_info) {
                    this.addLog('info', '方法信息:');
                    Object.entries(data.method_info).forEach(([key, value]) => {
                        this.addLog('info', `  ${key}: ${value}`);
                    });
                } else if (data.message) {
                    this.addLog('info', data.message);
                } else if (data.step) {
                    this.addLog('info', `步骤 ${data.step} 输出已接收`);
                }
            }
            
            handleResult(data) {
                const testCases = data.test_cases || [];
                const summary = data.summary || {};
                
                this.addLog('success', `任务完成! 生成了 ${testCases.length} 个测试用例`);
                this.displayResults(testCases, summary);
                this.setUIState('completed');
            }
            
            handleError(data) {
                const error = data.error || '未知错误';
                this.addLog('error', `任务失败: ${error}`);
                this.setUIState('failed');
            }
            
            displayResults(testCases, summary) {
                const resultsSection = document.getElementById('resultsSection');
                const container = document.getElementById('testCasesContainer');
                
                container.innerHTML = '';
                
                testCases.forEach((testCase, index) => {
                    const testCaseElement = document.createElement('div');
                    testCaseElement.className = 'test-case';
                    testCaseElement.innerHTML = `
                        <h4>测试用例 ${index + 1}</h4>
                        <p><strong>测试结果:</strong> ${testCase.test_result || 'N/A'}</p>
                        <p><strong>发现Bug:</strong> ${testCase.find_bug ? '是' : '否'}</p>
                        <details>
                            <summary>查看测试代码</summary>
                            <pre><code>${testCase.test_case || 'N/A'}</code></pre>
                        </details>
                    `;
                    container.appendChild(testCaseElement);
                });
                
                resultsSection.classList.remove('hidden');
            }
            
            async cancelTask() {
                if (!this.currentTaskId) return;
                
                try {
                    const response = await fetch(`${this.baseUrl}/tasks/${this.currentTaskId}`, {
                        method: 'DELETE'
                    });
                    
                    if (response.ok) {
                        this.addLog('warning', '任务已取消');
                        this.setUIState('cancelled');
                    }
                } catch (error) {
                    this.addLog('error', `取消任务失败: ${error.message}`);
                }
            }
            
            setUIState(state) {
                const submitBtn = document.getElementById('submitBtn');
                const cancelBtn = document.getElementById('cancelBtn');
                
                switch (state) {
                    case 'idle':
                        submitBtn.disabled = false;
                        submitBtn.textContent = '🚀 开始生成测试用例';
                        cancelBtn.disabled = true;
                        break;
                    case 'creating':
                        submitBtn.disabled = true;
                        submitBtn.textContent = '⏳ 创建任务中...';
                        cancelBtn.disabled = true;
                        break;
                    case 'running':
                        submitBtn.disabled = true;
                        submitBtn.textContent = '🔄 任务执行中...';
                        cancelBtn.disabled = false;
                        break;
                    case 'completed':
                    case 'failed':
                    case 'cancelled':
                        submitBtn.disabled = false;
                        submitBtn.textContent = '🚀 开始生成测试用例';
                        cancelBtn.disabled = true;
                        if (this.websocket) {
                            this.websocket.close();
                        }
                        break;
                }
            }
            
            addLog(type, message) {
                const container = document.getElementById('logContainer');
                const logEntry = document.createElement('div');
                logEntry.className = `log-entry ${type}`;
                
                const timestamp = new Date().toLocaleTimeString();
                logEntry.textContent = `[${timestamp}] ${message}`;
                
                container.appendChild(logEntry);
                container.scrollTop = container.scrollHeight;
            }
        }
        
        // 初始化UI
        document.addEventListener('DOMContentLoaded', () => {
            new TestAgentUI();
        });
    </script>
</body>
</html>
