import logging
import j<PERSON>
from LoggerManager import <PERSON>ggerManager
from CKGRetriever import CKGRetriever
from EnvironmentService import EnvironmentService
from Planner import PlannerState
from Planner import graph
from Config import *
from Utils import add_record, add_test_case

def get_graph_retriever(url, username, password):
    """
    Create a new instance of CKGRetriever with the provided URL, username, and password.
    """
    return CKGRetriever(url, username, password)

def generate_test_case(method_id, url, username, password, limit):
    config = {"recursion_limit": limit}
    graph_retriever = get_graph_retriever(url, username, password)
    method = graph_retriever.search_method_by_id(method_id)
    envServer = EnvironmentService()
    envServer.set_base_path(method.absolute_path.split("src")[0])
    graph_retriever.change_focal_method_id(int(method.id))
    inject_path = method.absolute_path.replace("src/main", "src/test")
    inject_dir = inject_path[:inject_path.rfind("/")]
    envServer.set_inject_dir(inject_dir)
    print("================= TEST GENERATE START =================\n")
    print(f"package_name: {method.package_name}\n")
    print(f"method_name: {method.name}\n")
    print(f"method_signature: {method.signature}\n")
    print(f"full_qualified_name: {method.full_qualified_name}\n")
    print(f"absolute_path: {method.absolute_path}\n\n")
    state = PlannerState(envServer=envServer,
                            package_name=method.package_name,
                            method_id=int(method.id),
                            method_code=method.content,
                            method_signature=method.signature,
                            class_name=method.class_name,
                            full_method_name=method.full_qualified_name,
                            start_line=method.start_line,
                            end_line=method.end_line)
    for event in graph.stream(state, config, subgraphs=True):
        print("========================== Graph:" + str(event[0]) + " =====================\n")
        print("Current Node: " + str(list(event[1].keys())[0]) + "\n")
        for value in event[1].values():
            if 'messages' in value.keys():
                for m in value["messages"]:
                    print(m.pretty_repr() + '\n\n')
            else:
                print('\n' + str(value) + '\n')
        event = event[1]
        EndPoint = 'testCaseAcceptor'
        if EndPoint in event.keys():
            print("===================== TEST GENERATE END =====================\n\n")
            result = []
            test_code = []
            find_bugs = []
            covered_lines = set()
            total_lines = set()
            mutation_info = {}
            line_coverage_rates = []
            branch_coverage_rates = []
            mutation_scores = []
            mutants_list = []
            test_point = []
            test_report = []
            inner_index = 0
            for test_case in event[EndPoint]['test_cases']:
                print(f"test case: {(test_case['test_case'])}\n")
                inner_index += 1
                print(f"===================== Test Report {inner_index} =====================\n")
                print(f"test result: {(test_case['test_result'])}\n")
                print(f"find bugs: {(test_case['find_bug'])}\n")
                print(f"test case: {(test_case['test_case'])}\n")
                print(f"test point: {(test_case['test_point'])}\n")
                print(f"test report: {(test_case['test_report'])}\n")
                print(f"coverage report: {(test_case['coverage_report'])}\n")
                print(f"mutation report: {(test_case['mutation_report'])}\n")
                result.append(test_case['test_result'])
                test_code.append(test_case['test_case'])
                find_bugs.append(test_case['find_bug'])
                test_point.append(test_case['test_point'])
                test_report.append(test_case['test_report'])
                if test_case['coverage_report']['result'] == 'Success':
                    line_coverage_rates.append(test_case['coverage_report']['output']['line_coverage'])
                    branch_coverage_rates.append(test_case['coverage_report']['output']['branch_coverage'])
                    total_lines.update(test_case['coverage_report']['output']['covered_lines'])
                    total_lines.update(test_case['coverage_report']['output']['missed_lines'])
                    covered_lines.update(test_case['coverage_report']['output']['covered_lines'])
                else:
                    line_coverage_rates.append(0)
                    branch_coverage_rates.append(0)
                if test_case['mutation_report']['result'] == 'Success':
                    mutation_scores.append(test_case['mutation_report']['output']['mutation_score'])
                    mutants_list.append(test_case['mutation_report']['output']['filtered_mutations'])
                    for item_mutation in test_case['mutation_report']['output']['filtered_mutations']:
                        key = str(item_mutation['Line']) + '_' + str(item_mutation['Mutator'])
                        if key in mutation_info.keys() and mutation_info[key] == 'KILLED':
                            continue
                        else:
                            mutation_info[key] = item_mutation['Status']
                else:
                    mutation_scores.append(0)

            print(f"test result: {(result)}\n")

            cal_coverage = len(covered_lines) / len(total_lines) if len(total_lines) != 0 else 0
            final_coverage = cal_coverage if cal_coverage > max(line_coverage_rates) / 100 else max(
                line_coverage_rates) / 100
            print(f"final coverage: {final_coverage}\n")
            final_mutation_score = sum([1 for v in mutation_info.values() if v == 'KILLED']) / len(
                mutation_info) if len(mutation_info) != 0 else 0
            print(f"final mutation score: {final_mutation_score}\n")

            return {
                "result": "Success",
                "output": {
                    "test_code": test_code,
                    "test_report": test_report,
                    "find_bugs": find_bugs,
                    "test_point": test_point,
                    "line_coverage_rate": line_coverage_rates,
                    "branch_coverage_rate": branch_coverage_rates,
                    "mutation_score": mutation_scores,
                    "final_coverage": final_coverage,
                    "final_mutation_score": final_mutation_score,
                    "mutation_info": mutation_info
                }
            }
    return {
        "result": "Failed",
        "output": "No test case generated."
    }




if __name__ == '__main__':
    # assert len(methods_list) < 201
    method_id = 44777
    ret = generate_test_case(method_id, "bolt://localhost:7687", "neo4j", "123456", 50)
